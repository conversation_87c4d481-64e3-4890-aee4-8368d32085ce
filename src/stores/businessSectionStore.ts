import type {
  BusinessItem,
  BusinessItemDetail,
  BusinessSection,
  BusinessSectionStore,
} from "@/types/BusinessSection.types";
import { create } from "zustand";
import { devtools } from "zustand/middleware";

// Configuration: Maximum entries per topic
const MAX_ENTRIES_PER_TOPIC = 3;

/**
 * Limits entries to the maximum allowed per topic, keeping the most recent ones
 */
function limitTopicEntries(
  entries: BusinessItemDetail[]
): BusinessItemDetail[] {
  if (!Array.isArray(entries)) return [];

  // // Sort by createdAt only (newest first) to keep positions stable on updates
  // const sorted = [...entries].sort((a: any, b: any) => {
  //   const aTime = Date.parse(String(a?.createdAt || 0));
  //   const bTime = Date.parse(String(b?.createdAt || 0));
  //   if (Number.isFinite(bTime - aTime) && bTime !== aTime) {
  //     return bTime - aTime;
  //   }
  //   return Number(b?.id) - Number(a?.id);
  // });

  // Take only the first MAX_ENTRIES_PER_TOPIC
  return entries.slice(0, MAX_ENTRIES_PER_TOPIC);
}

export const useBusinessSectionStore = create<BusinessSectionStore>()(
  devtools(
    (set, get) => ({
      sections: [],
      isLoading: false,
      error: null,

      setSections: (sections) =>
        set((state) => {
          const current = state.sections;
          const changed =
            !Array.isArray(current) ||
            current.length !== sections.length ||
            JSON.stringify(current) !== JSON.stringify(sections);
          if (!changed) {
            console.log("[BusinessSectionStore] setSections noop (no change)");
            return state;
          }
          console.log("[BusinessSectionStore] setSections", {
            count: sections.length,
          });
          return {
            sections,
            error: null,
          } as Partial<BusinessSectionStore> as any;
        }),
      setLoading: (isLoading) =>
        set((state) => {
          if (state.isLoading === isLoading) return state;
          console.log("[BusinessSectionStore] setLoading", { isLoading });
          return { isLoading };
        }),
      setError: (error) =>
        set((state) => {
          if (state.error === error) return state;
          console.log("[BusinessSectionStore] setError", { error });
          return { error };
        }),

      updateItem: (sectionId, itemId, updates) => {
        set((state) => ({
          sections: state.sections.map((section) =>
            section.id === sectionId
              ? {
                  ...section,
                  items: section.items.map((item) =>
                    item.id === itemId ? { ...item, ...updates } : item
                  ),
                }
              : section
          ),
        }));
      },

      addItem: (sectionId, item) => {
        set((state) => ({
          sections: state.sections.map((section) =>
            section.id === sectionId
              ? { ...section, items: [...section.items, item] }
              : section
          ),
        }));
      },

      removeItem: (sectionId, itemId) => {
        set((state) => ({
          sections: state.sections.map((section) =>
            section.id === sectionId
              ? {
                  ...section,
                  items: section.items.filter((item) => item.id !== itemId),
                }
              : section
          ),
        }));
      },

      // Topic entries management for each topic (item)
      getTopicEntries: (sectionId, topicId) => {
        const state = get();
        const section = state.sections.find((s) => s.id === sectionId);
        const topic = section?.items.find((item) => item.id === topicId);

        console.log(`🔥🔥🔥 [BusinessSectionStore] getTopicEntries DEBUG:`, {
          requestedSectionId: sectionId,
          requestedTopicId: topicId,
          availableSections: state.sections.map((s) => ({
            id: s.id,
            itemsCount: s.items?.length || 0,
          })),
          foundSection: !!section,
          sectionItems: section?.items?.map((item) => ({
            id: item.id,
            hasEntries: !!(item as any).entries,
            entriesCount: (item as any).entries?.length || 0,
          })),
          foundTopic: !!topic,
          topicEntries: (topic as any)?.entries?.length || 0,
        });

        return (topic as any)?.entries || [];
      },

      setTopicEntries: (sectionId, topicId, entries) => {
        const safeEntries = Array.isArray(entries) ? entries : [];
        // Apply entry limit - keep only the most recent MAX_ENTRIES_PER_TOPIC
        const limitedEntries = limitTopicEntries(safeEntries);

        console.log(`🔥 [BusinessSectionStore] setTopicEntries BEFORE:`, {
          sectionId,
          topicId,
          originalCount: safeEntries.length,
          limitedCount: limitedEntries.length,
          maxAllowed: MAX_ENTRIES_PER_TOPIC,
          entries: limitedEntries.slice(0, 3), // First 3 entries for logging
          currentSections: get().sections.map((s) => ({
            id: s.id,
            title: s.title,
            itemsCount: s.items?.length || 0,
            targetItem: s.items?.find((item) => item.id === topicId),
          })),
        });

        set((state) => {
          const newSections = state.sections.map((section) => {
            if (section.id !== sectionId) return section;
            const existingItems = Array.isArray(section.items)
              ? section.items
              : [];
            const hasItem = existingItems.some((item) => item.id === topicId);

            const nextItems = hasItem
              ? existingItems.map((item) =>
                  item.id === topicId
                    ? ({ ...item, entries: limitedEntries } as BusinessItem & {
                        entries: BusinessItemDetail[];
                      })
                    : item
                )
              : [
                  ...existingItems,
                  {
                    id: topicId,
                    title: `Topic ${topicId}`,
                    status: "idea",
                    actions: 0,
                    ideas: 0,
                    results: 0,
                    // We don't rely on icon in the store; grid sets icon from titles. Cast to any to satisfy type.
                    icon: null as any,
                    entries: limitedEntries,
                  } as unknown as BusinessItem & {
                    entries: BusinessItemDetail[];
                  },
                ];

            return { ...section, items: nextItems } as BusinessSection;
          });

          console.log(`🔥 [BusinessSectionStore] setTopicEntries AFTER:`, {
            sectionId,
            topicId,
            sectionsUpdated: newSections.map((s) => ({
              id: s.id,
              title: s.title,
              itemsCount: s.items?.length || 0,
              updatedItem: s.items?.find(
                (item) => item.id === topicId && (item as any).entries
              ),
            })),
          });

          return { sections: newSections };
        });
        console.log(
          `[BusinessSectionStore] setTopicEntries: ${sectionId}/${topicId}`,
          `${safeEntries.length} → ${limitedEntries.length} entries (max: ${MAX_ENTRIES_PER_TOPIC})`
        );
      },

      clearAllTopicEntries: () => {
        set((state) => ({
          sections: state.sections.map((section) => ({
            ...section,
            items: (section.items || []).map((item) => ({
              ...item,
              entries: [],
            })),
          })),
        }));
      },

      // Apply entry limit to all existing entries in the store
      applyEntryLimitsToAllTopics: () => {
        console.log(
          "[BusinessSectionStore] Applying entry limits to all existing topics"
        );

        set((state) => {
          let totalLimited = 0;
          const newSections = state.sections.map((section) => ({
            ...section,
            items: (section.items || []).map((item) => {
              const currentEntries = (item as any)?.entries || [];
              if (
                !Array.isArray(currentEntries) ||
                currentEntries.length <= MAX_ENTRIES_PER_TOPIC
              ) {
                return item;
              }

              const limitedEntries = limitTopicEntries(currentEntries);
              const removedCount =
                currentEntries.length - limitedEntries.length;
              if (removedCount > 0) {
                totalLimited += removedCount;
                console.log(
                  `[BusinessSectionStore] Limited ${item.id}: ${currentEntries.length} → ${limitedEntries.length} entries`
                );
              }

              return {
                ...item,
                entries: limitedEntries,
              } as any;
            }),
          }));

          if (totalLimited > 0) {
            console.log(
              `[BusinessSectionStore] Applied limits: removed ${totalLimited} entries total`
            );
          }

          return { sections: newSections };
        });
      },

      // Find a topic item anywhere in sections by topicId; returns its sectionId and item
      findTopicItemById: (topicId) => {
        const state = get();
        const idStr = String(topicId);
        for (const section of state.sections) {
          const item = (section.items || []).find(
            (it) => String(it.id) === idStr
          ) as any;
          if (item) {
            return { sectionId: section.id, item } as any;
          }
        }
        return null;
      },

      addTopicEntry: (sectionId, topicId, entry) => {
        const state = get();
        const currentEntries = state.sections
          .find((s) => s.id === sectionId)
          ?.items.find((item) => item.id === topicId) as any;
        const entries = currentEntries?.entries || [];
        const updatedEntries = [...entries, entry];
        // setTopicEntries will automatically apply the entry limit
        get().setTopicEntries(sectionId, topicId, updatedEntries);
      },

      updateTopicEntry: (sectionId, topicId, entryId, updates) => {
        const state = get();
        const currentItem = state.sections
          .find((s) => s.id === sectionId)
          ?.items.find((item) => item.id === topicId) as any;
        const currentEntries = currentItem?.entries || [];
        const updatedEntries = currentEntries.map((entry: BusinessItemDetail) =>
          entry.id === entryId ? { ...entry, ...updates } : entry
        );
        // setTopicEntries will automatically apply the entry limit
        get().setTopicEntries(sectionId, topicId, updatedEntries);
      },

      removeTopicEntry: (sectionId, topicId, entryId) => {
        const state = get();

        const currentItem = state.sections
          .find((s) => s.id === sectionId)
          ?.items.find((item) => item.id === topicId) as any;

        const currentEntries = currentItem?.entries || [];
        const updatedEntries = currentEntries.filter(
          (entry: BusinessItemDetail) => entry.id !== entryId
        );

        get().setTopicEntries(sectionId, topicId, updatedEntries);
      },
    }),
    {
      name: "business-section-store",
    }
  )
);
