"use client";

import { MessageSquare, Send } from "lucide-react";
import { useState } from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  She<PERSON>,
  <PERSON><PERSON><PERSON>lose,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Textarea } from "@/components/ui/textarea";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { SidebarButton } from "../ui/sidebar-button";

type FeedbackFABProps = {
  projectId: string;
};

export function FeedbackFAB({ projectId }: FeedbackFABProps) {
  const [open, setOpen] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const [form, setForm] = useState({
    message: "",
    review: "",
    hypotheticalPay: "",
    isReview: false,
    displayName: "",
    company: "",
  });

  const reset = () => {
    setForm({
      message: "",
      review: "",
      hypotheticalPay: "",
      isReview: false,
      displayName: "",
      company: "",
    });
    setError(null);
    setSuccess(false);
  };

  const isFormValid = () => {
    if (!form.message.trim()) return false;
    if (!form.review.trim()) return false;
    if (form.isReview && !form.displayName.trim()) return false;
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!form.message.trim()) {
      setError("Please enter your feedback.");
      return;
    }

    setSubmitting(true);
    setError(null);
    setSuccess(false);
    try {
      const combinedMessage = `Feedback:\n${form.message}\n\nReview:\n${
        form.review
      }\n\nHypothetical payment:\n${form.hypotheticalPay || "-"}`;
      const payload = {
        projectId,
        message: combinedMessage,
        testimonial: form.isReview,
        displayName: form.displayName || undefined,
        company: form.company || undefined,
        allowPublic: form.isReview,
        source: "project",
      } as const;

      const res = await fetch("/api/feedback", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });

      const json = await res.json();
      if (res.ok && json?.success) {
        setSuccess(true);
        reset();
        setOpen(false);
      } else {
        throw new Error(json?.error || "Failed to send feedback");
      }
    } catch (err: any) {
      setError(err?.message || "Failed to send feedback");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Sheet
      open={open}
      onOpenChange={(v) => {
        if (!v) reset();
        setOpen(v);
      }}
    >
      <Tooltip>
        <TooltipTrigger asChild>
          <SheetTrigger asChild>
            <Button
              size="lg"
              className="fixed bottom-6 right-6 z-50 rounded-full bg-[var(--primary)]/50 text-[var(--primary-foreground)] hover:bg-[var(--hover-primary)] hover:opacity-100 shadow-lg hover:shadow-xl hover:text-[var(--primary)]"
              aria-label="Send feedback"
            >
              <MessageSquare className="size-5" />
            </Button>
          </SheetTrigger>
        </TooltipTrigger>
        <TooltipContent sideOffset={8}>Send feedback</TooltipContent>
      </Tooltip>

      <SheetContent side="bottom" className="max-w-3xl mx-auto rounded-t-xl">
        <SheetHeader>
          <SheetTitle>Share your feedback</SheetTitle>
        </SheetHeader>

        <form onSubmit={handleSubmit} className="px-4 pb-4 space-y-4">
          <div className="grid gap-2">
            <label className="text-md text-[var(--muted-foreground)]">
              Feedback
            </label>
            <Textarea
              placeholder="Tell us what's working, what's not, or what you'd like to see…"
              value={form.message}
              onChange={(e) =>
                setForm((f) => ({ ...f, message: e.target.value }))
              }
              className="min-h-[120px] text-md"
              required
            />
          </div>

          <div className="grid gap-2">
            <label className="text-md text-[var(--muted-foreground)]">
              Review <span className="text-[var(--destructive)]">*</span>
            </label>
            <Textarea
              placeholder={
                "We’d really appreciate a quick review sharing the value you see, or any other praise you may have 🙏"
              }
              value={form.review}
              onChange={(e) =>
                setForm((f) => ({ ...f, review: e.target.value }))
              }
              className="min-h-[100px] text-md"
              required
            />
          </div>

          <div className="grid gap-2">
            <label className="text-md text-[var(--muted-foreground)]">
              Hypothetically, would you actually pay for this? Why or why not?
            </label>
            <Textarea
              placeholder="Be honest – your input helps us prioritize the right things."
              value={form.hypotheticalPay}
              onChange={(e) =>
                setForm((f) => ({ ...f, hypotheticalPay: e.target.value }))
              }
              className="min-h-[80px] text-md"
            />
          </div>

          <div
            className="flex items-start gap-3 rounded-md border border-[var(--border)] p-3 cursor-pointer hover:bg-[var(--muted)]/50 transition-colors"
            onClick={() => setForm((f) => ({ ...f, isReview: !f.isReview }))}
          >
            <input
              id="isTestimonial"
              type="checkbox"
              checked={form.isReview}
              onChange={(e) =>
                setForm((f) => ({ ...f, isReview: e.target.checked }))
              }
              className="mt-1 h-4 w-4 accent-[var(--primary)] cursor-pointer"
            />
            <label
              htmlFor="isReview"
              className="text-md leading-6 cursor-pointer flex-1"
            >
              I consent to my review being shared publicly by siift
            </label>
          </div>

          {form.isReview && (
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="grid gap-2">
                <label className="text-md text-[var(--muted-foreground)]">
                  Display name{" "}
                  <span className="text-[var(--destructive)]">*</span>
                </label>
                <Input
                  placeholder="Jane Doe"
                  value={form.displayName}
                  onChange={(e) =>
                    setForm((f) => ({ ...f, displayName: e.target.value }))
                  }
                  required={form.isReview}
                  className={
                    form.isReview && !form.displayName.trim()
                      ? "border-[var(--destructive)] text-md"
                      : ""
                  }
                />
              </div>
              <div className="grid gap-2">
                <label className="text-md text-[var(--muted-foreground)]">
                  Company (optional)
                </label>
                <Input
                  placeholder="Acme Inc."
                  className="text-md"
                  value={form.company}
                  onChange={(e) =>
                    setForm((f) => ({ ...f, company: e.target.value }))
                  }
                />
              </div>
            </div>
          )}

          {error && (
            <div className="text-[var(--destructive)] text-sm">{error}</div>
          )}

          {success && (
            <div className="text-[var(--primary)] text-sm">
              Thanks for your feedback!
            </div>
          )}

          <SheetFooter className="p-0">
            <div className="ml-auto flex items-center gap-2">
              <SheetClose asChild>
                <SidebarButton
                  variant="outline"
                  type="button"
                  size="md"
                  className="text-sm"
                >
                  Cancel
                </SidebarButton>
              </SheetClose>
              <SidebarButton
                type="submit"
                hoverColor="grey"
                hoverScale={true}
                trailing={<Send className="size-4" />}
                size="md"
                className="bg-[var(--primary)]/10   "
                showBorder={true}
                disabled={submitting || !isFormValid()}
              >
                {submitting ? "Sending…" : "Send feedback"}
              </SidebarButton>
            </div>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  );
}

export default FeedbackFAB;
