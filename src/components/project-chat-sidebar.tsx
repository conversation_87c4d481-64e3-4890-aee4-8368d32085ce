"use client";

import { AiIntakeProgress } from "@/components/ai/AiIntakeProgress";
import { Button } from "@/components/ui/button";
import { useSidebar } from "@/components/ui/sidebar";
import { useAiChat } from "@/hooks/useAiChat";
import { useAiIntake } from "@/hooks/useAiIntake";
import { safeLocalStorage } from "@/lib/storage";
import { useChatStore } from "@/stores/chatStore";
import { useFocusQueueStore } from "@/stores/focusQueueStore";
import type { ChatMessage } from "@/types/Chat.types";
import { useEffect, useState } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { AI_Prompt } from "./ui/animated-ai-input";
import { ChatBubble, ChatBubbleMessage } from "./ui/chat-bubble";
import { ChatMessageList } from "./ui/chat-message-list";
import { Logo } from "./ui/logo";

interface ProjectChatSidebarProps {
  projectId: string;
  isCollapsed?: boolean;
  onCollapseChange?: (collapsed: boolean) => void;
  embedded?: boolean; // New prop to indicate if it's embedded in sidebar
  chatWidth?: "45%" | "45%";
  setChatWidth?: (width: "45%" | "45%") => void;
  isChatCollapsed?: boolean;
  setIsChatCollapsed?: (collapsed: boolean) => void;
  selectedBusinessItem?: any;
  projectTitle?: string; // Add project title prop
}

// Welcome message based on context
const getWelcomeMessage = (
  isDetailsPage: boolean = false,
  topicName?: string
): ChatMessage => ({
  id: "welcome",
  user: "siift AI",
  avatar: "",
  message:
    isDetailsPage && topicName
      ? `So far, we have a few ideas for ${topicName} in the focus table on the right - let's validate them with by setting some actions and then recording the results!`
      : "", // : "Ok we've filled in a few key topics, click on one of them to dive in!",
  timestamp: new Date().toLocaleTimeString([], {
    hour: "2-digit",
    minute: "2-digit",
  }),
  isCurrentUser: false,
});

export function ProjectChatSidebar({
  projectId,
  embedded = false,
  selectedBusinessItem,
  projectTitle,
}: ProjectChatSidebarProps) {
  const { state } = useSidebar();
  const enqueueFocus = useFocusQueueStore((s) => s.enqueue);

  // Chat store integration
  const chatStore = useChatStore();
  const messages = chatStore.messages;

  // AI chat hook for streaming
  const topicName = selectedBusinessItem?.title as string | undefined;
  const { sendMessage, cancelMessage, isStreaming } = useAiChat(projectId, {
    topicName,
  });
  const { stage } = useAiIntake();
  const [showIntakeProgress, setShowIntakeProgress] = useState(false);

  // Auto-show intake progress when stage is active
  useEffect(() => {
    const isIntakeActive = stage && !["idle", "ready"].includes(stage);
    setShowIntakeProgress(isIntakeActive);
  }, [stage]);

  // Initialize messages on mount with onboarding persistence (topic-scoped)
  useEffect(() => {
    if (!projectId) return;
    // Switch to the proper scope per project/topic
    chatStore.setScope?.(projectId, topicName ?? null);

    // After switching scope, check if the CURRENT SCOPE has any messages
    const scopeKey = chatStore.getScopeKey?.(projectId, topicName ?? null);
    const scopeHasAny = (useChatStore.getState().messages?.length ?? 0) > 0;
    console.log("scopeHasAny", scopeHasAny, "scopeKey", scopeKey);
    if (scopeHasAny) return;

    // Load from scoped localStorage if empty for this scope
    const lsKey =
      scopeKey && scopeKey.includes("|t:")
        ? `chat_messages_${projectId}_topic_${scopeKey.split("|t:")[1]}`
        : `chat_messages_${projectId}`;
    const savedMessages = safeLocalStorage.getJSON<ChatMessage[]>(lsKey, []);

    // Load persisted onboarding messages for this scope (topic-level if present)
    const topicSlug =
      scopeKey && scopeKey.includes("|t:") ? scopeKey.split("|t:")[1] : null;
    const onboardingKey = topicSlug
      ? `onboarding_messages_${projectId}_topic_${topicSlug}`
      : `onboarding_messages_${projectId}`;
    const onboardingMessages = safeLocalStorage.getJSON<ChatMessage[]>(
      onboardingKey,
      []
    );

    // Filter out onboarding messages from saved messages to avoid duplicates
    const nonOnboardingMessages = savedMessages.filter(
      (msg) => !msg.isOnboarding
    );

    // If there is any saved history (or persisted onboarding), show it immediately
    if (
      nonOnboardingMessages.length > 0 ||
      (onboardingMessages && onboardingMessages.length > 0)
    ) {
      const welcomeMessage = getWelcomeMessage(
        !!selectedBusinessItem,
        selectedBusinessItem?.title
      );
      const allMessages = [
        ...(nonOnboardingMessages.length === 0 &&
        onboardingMessages.length === 0
          ? [welcomeMessage]
          : []),
        ...onboardingMessages,
        ...nonOnboardingMessages,
      ].sort((a, b) => {
        const getTime = (msg: ChatMessage) => {
          if (msg.id.includes("onboarding-")) {
            return parseInt(msg.id.split("onboarding-")[1]) || 0;
          }
          return parseInt(msg.id.split("-")[1]) || 0;
        };
        return getTime(a) - getTime(b);
      });
      chatStore.setMessages(allMessages);
      // Nudge focus to the table even when loading existing history
      setTimeout(() => {
        return enqueueFocus("siftt", 3000, 5);
      }, 300);
      return;
    }

    // Otherwise, delay onboarding seeding by 1 second to avoid flicker
    const timeoutId = window.setTimeout(() => {
      // Re-check in case messages arrived in the meantime
      const stillEmpty =
        (useChatStore.getState().messages?.length ?? 0) === 0 && !!scopeKey;
      if (!stillEmpty) return;

      const welcomeMessage = getWelcomeMessage(
        !!selectedBusinessItem,
        selectedBusinessItem?.title
      );
      const allMessages = [welcomeMessage];
      chatStore.setMessages(allMessages);
      enqueueFocus("siftt", 3000, 5);
    }, 1000);

    return () => {
      window.clearTimeout(timeoutId);
    };
  }, [projectId, topicName, selectedBusinessItem]);

  // Handle sending messages - now using the useAiChat hook
  const handleSendMessage = sendMessage;
  // Use external state if provided, otherwise use internal state

  // Chat is always expanded - no collapse functionality

  // Render embedded version for sidebar
  if (embedded) {
    // Don't show anything when sidebar is collapsed
    if (state === "collapsed") {
      return null;
    }

    // Always show expanded chat - no collapse functionality
    return (
      <div
        className={`w-full h-full border-t border-[var(--siift-light-mid)]/50 ${
          selectedBusinessItem
            ? "bg-[var(--siift-lightest-accent)]/40 dark:bg-[color:var(--siift-dark-accent)]/20"
            : "bg-[var(--siift-light-accent)]/10 dark:bg-[color:var(--siift-darker)]/40"
        } backdrop-blur-sm flex flex-col transition-all duration-300`}
      >
        {/* AI Intake Progress - dev only */}
        {process.env.NODE_ENV === "development" && (
          <div className="p-3 border-b border-border/50">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">AI Processing</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowIntakeProgress(!showIntakeProgress)}
                className="h-6 text-xs"
              >
                {showIntakeProgress ? "Hide" : "Show"}
              </Button>
            </div>
            {showIntakeProgress && <AiIntakeProgress projectId={projectId} />}
          </div>
        )}

        {/* Messages */}
        <div className="flex-1 overflow-hidden w-full min-h-0 relative">
          <ChatMessageList className="w-full h-full">
            {(() => {
              // Move console.log outside of render to prevent infinite loops
              if (typeof window !== "undefined") {
                setTimeout(() => {
                  console.log("[ChatComponent] Rendering messages:", {
                    totalMessages: messages.length,
                    lastFewMessages: messages.slice(-3).map((m) => ({
                      id: m.id,
                      isUser: m.isCurrentUser,
                      content: m.message.slice(0, 40) + "...",
                      length: m.message.length,
                    })),
                  });
                }, 0);
              }
              return messages;
            })().map((msg) => (
              <ChatBubble
                key={msg.id}
                className="mt-2"
                variant={msg.isCurrentUser ? "sent" : "received"}
              >
                {!msg.isCurrentUser && (
                  <div className="h-8 w-8 shrink-0 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Logo size={20} />
                  </div>
                )}
                <ChatBubbleMessage
                  variant={msg.isCurrentUser ? "sent" : "received"}
                >
                  <div className="text-md leading-relaxed">
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      components={{
                        // Custom styling for markdown elements
                        p: ({ children }) => (
                          <p className="mb-2 last:mb-0">{children}</p>
                        ),
                        ul: ({ children }) => (
                          <ul className="mb-2 last:mb-0 ml-4 list-disc space-y-1">
                            {children}
                          </ul>
                        ),
                        ol: ({ children }) => (
                          <ol className="mb-2 last:mb-0 ml-4 list-decimal space-y-1">
                            {children}
                          </ol>
                        ),
                        li: ({ children }) => (
                          <li className="leading-relaxed">{children}</li>
                        ),
                        strong: ({ children }) => (
                          <strong className="font-semibold">{children}</strong>
                        ),
                        em: ({ children }) => (
                          <em className="italic">{children}</em>
                        ),
                        code: ({ children }) => (
                          <code className="bg-muted px-1 py-0.5 rounded text-sm font-mono">
                            {children}
                          </code>
                        ),
                        pre: ({ children }) => (
                          <pre className="bg-muted p-3 rounded-md overflow-x-auto my-2">
                            {children}
                          </pre>
                        ),
                        blockquote: ({ children }) => (
                          <blockquote className="border-l-4 border-primary/20 pl-4 italic text-muted-foreground my-2">
                            {children}
                          </blockquote>
                        ),
                        h1: ({ children }) => (
                          <h1 className="text-lg font-bold mb-2 mt-4 first:mt-0">
                            {children}
                          </h1>
                        ),
                        h2: ({ children }) => (
                          <h2 className="text-md font-semibold mb-2 mt-3 first:mt-0">
                            {children}
                          </h2>
                        ),
                        h3: ({ children }) => (
                          <h3 className="text-sm font-medium mb-1 mt-2 first:mt-0">
                            {children}
                          </h3>
                        ),
                      }}
                    >
                      {msg.message.trim()}
                    </ReactMarkdown>
                  </div>
                  {!msg.isCurrentUser && msg.cta?.type === "refetch_topics" && (
                    <div className="mt-2">
                      <Button
                        size="sm"
                        className="h-7"
                        onClick={async () => {
                          // Aggressively invalidate and refetch topics and entries using our shared client
                          const { queryClient } = await import(
                            "@/lib/queryClient"
                          );
                          const keys: any[] = [
                            ["all-project-topics-v2", projectId],
                            ["all-topic-entries", projectId],
                            ["topics", projectId],
                            ["business-sections", projectId],
                          ];
                          keys.forEach((k) =>
                            queryClient.invalidateQueries({ queryKey: k })
                          );
                          keys.forEach((k) =>
                            queryClient.refetchQueries({ queryKey: k })
                          );
                        }}
                      >
                        {msg.cta.label || "Start siifting"}
                      </Button>
                    </div>
                  )}
                </ChatBubbleMessage>
              </ChatBubble>
            ))}
          </ChatMessageList>
        </div>

        {/* Message Input */}
        <div className="p-3 border-t border-border/50 bg-muted/30 backdrop-blur-sm w-full flex-shrink-0">
          <AI_Prompt
            onSendMessage={handleSendMessage}
            onStop={() => cancelMessage()}
            isLoading={isStreaming}
            placeholder={
              isStreaming
                ? "AI is responding..."
                : selectedBusinessItem
                ? `Ask me about ${selectedBusinessItem.title}...`
                : projectTitle
                ? `Ask me anything about ${projectTitle}...`
                : "Ask me anything about your project..."
            }
          />

          {/* Disclaimer */}
          <div className="mt-1 px-2">
            <p className="text-[10px] text-center opacity-50 leading-tight">
              siift can make mistakes. Please double check answers to ensure
              accuracy.
            </p>
          </div>
        </div>
      </div>
    );
  }
}
