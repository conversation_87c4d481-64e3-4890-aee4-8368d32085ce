"use client";

import { Logo } from "@/components/ui/logo";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { DottedBackground } from "@/components/ui/dotted-background";
import { useAiIntake } from "@/hooks/useAiIntake";
import { authApi } from "@/lib/api";
import { useProjectCreationStore } from "@/stores/projectCreationStore";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export function ProjectCreationQuestionnaire() {
  const {
    ideaText,
    questionnaireData,
    setQuestionnaireAnswer,
    completeQuestionnaire,
    createdProject,
    setCreatedProject,
    projectCreationStartTime,
    setProjectCreationStartTime,
    projectCreationStarted,
    setProjectCreationStarted,
  } = useProjectCreationStore();

  const [isCompleted, setIsCompleted] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [projectName, setProjectName] = useState(
    questionnaireData.projectName || ""
  );
  const router = useRouter();

  const { startIntake } = useAiIntake();

  // Redirect when project is created and questionnaire is completed
  useEffect(() => {
    if (isCompleted && createdProject) {
      // Project is ready, redirect to the created project
      setTimeout(() => {
        router.push(`/projects/${createdProject.id}`);
      }, 2000); // Show success for 2 seconds
    }
  }, [isCompleted, createdProject, router]);

  // Track start when project name is provided (replacing stepper change logic)
  useEffect(() => {
    if (projectName && projectName.trim() && !projectCreationStarted) {
      const startTime = Date.now();
      setProjectCreationStartTime(startTime);
      setProjectCreationStarted(true);
    }
  }, [
    projectName,
    projectCreationStarted,
    setProjectCreationStartTime,
    setProjectCreationStarted,
  ]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setQuestionnaireAnswer("projectName" as any, projectName || "");

    try {
      setIsCreating(true);
      setIsCompleted(true);

      const finalName =
        projectName || questionnaireData.projectName || "My Project";

      const project = await authApi.createProject({
        name: finalName,
        description: ideaText || "",
      });

      const projId = (project as any).id || (project as any).projectId;

      const returnedName = (project as any).name;
      if (returnedName !== finalName && projId) {
        try {
          await authApi.patchProject(String(projId), {
            name: finalName,
          });
        } catch (patchError) {
          console.error("Failed to update project name:", patchError);
        }
      }

      const realProject = {
        id: projId || `${Date.now()}`,
        name: finalName,
        description: (project as any).description || ideaText || "",
      };

      setCreatedProject(realProject);

      if (projId) {
        void startIntake(String(projId), ideaText || "");
      }

      await new Promise((resolve) => setTimeout(resolve, 500));
      router.push(`/projects/${projId}`);
    } catch (e) {
      console.error("Failed to create project:", e);
    } finally {
      setIsCreating(false);
      completeQuestionnaire();
    }
  };

  // Show project creation animation if completed but project not ready
  if (isCompleted && !createdProject) {
    return (
      <div className="fixed inset-0 z-50 bg-background overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-muted/20" />

        {/* Noise Texture Overlay */}
        <div
          className="absolute inset-0 opacity-[0.02] dark:opacity-[0.04]"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
            backgroundSize: "256px 256px",
          }}
        />

        {/* Content */}
        <div className="relative z-10 flex items-center justify-center min-h-screen">
          <div className="text-center">
            {/* App Logo */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="mb-8"
            >
              <Logo
                size={64}
                animated={true}
                showText={false}
                className="mx-auto"
              />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="space-y-4"
            >
              <h2 className="text-2xl font-bold text-[var(--primary)]">
                Creating your project...
              </h2>
              <p className="text-muted-foreground">
                We're setting up everything for you
              </p>
            </motion.div>
          </div>
        </div>
      </div>
    );
  }

  // Show success message when project is ready
  if (isCompleted && createdProject) {
    return (
      <div className="fixed inset-0 z-50 bg-background overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-muted/20" />

        {/* Noise Texture Overlay */}
        <div
          className="absolute inset-0 opacity-[0.02] dark:opacity-[0.04]"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
            backgroundSize: "256px 256px",
          }}
        />

        {/* Content */}
        <div className="relative z-10 flex items-center justify-center min-h-screen">
          <div className="text-center">
            {/* Success Icon */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{
                type: "spring",
                stiffness: 200,
                damping: 20,
              }}
              className="w-16 h-16 mx-auto mb-6 rounded-full bg-[var(--primary)] flex items-center justify-center"
            >
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2 }}
                className="text-[var(--primary-foreground)] text-2xl"
              >
                ✓
              </motion.div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="space-y-4"
            >
              <h2 className="text-2xl font-bold text-[var(--primary)]">
                Your project is ready!
              </h2>
              <p className="text-muted-foreground">
                {createdProject.name} has been created successfully
              </p>
              <p className="text-sm text-muted-foreground">
                Redirecting to dashboard...
              </p>
            </motion.div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative min-h-screen overflow-hidden">
      <DottedBackground
        fadeEdge={20}
        dotSizes={[1, 1.5, 2]}
        spacing={25}
        dotsPerRow={8}
        opacity={0.02}
        darkOpacity={0.15}
      />
      <div className="relative z-10 flex min-h-screen items-center justify-center px-4">
        <div className="w-full max-w-sm">
          <div className="mb-8 text-center">
            <Logo
              size={40}
              animated={true}
              showText={false}
              className="mx-auto"
            />
            <h1 className="mt-4 text-2xl font-semibold">Name your project</h1>
            <p className="text-muted-foreground">
              Choose a name that represents your vision
            </p>
          </div>

          <div className="group relative bg-card/80 backdrop-blur border-2 border-[var(--siift-mid-accent)]/90 rounded-2xl p-6 space-y-4 shadow-xl overflow-hidden transition-all duration-300">
            <div className="absolute inset-0 bg-gradient-to-br from-[var(--primary)]/5 via-transparent to-[var(--primary-light)]/5 pointer-events-none m-0" />

            <div className="relative z-10">
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="project-name">Project name</Label>
                  <Input
                    id="project-name"
                    placeholder="Enter your project name..."
                    value={projectName}
                    onChange={(e) => {
                      setProjectName(e.target.value);
                      setQuestionnaireAnswer(
                        "projectName" as any,
                        e.target.value
                      );
                    }}
                    disabled={isCreating}
                  />
                </div>

                <Button
                  type="submit"
                  className="w-full"
                  disabled={isCreating || !projectName.trim()}
                >
                  {isCreating ? "Creating..." : "Create project"}
                </Button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
