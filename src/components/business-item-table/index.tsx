"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Table, TableBody } from "@/components/ui/table";
import { TooltipProvider } from "@/components/ui/tooltip";
import {
  useAllProjectTopics,
  useTopicEntryMutations,
} from "@/hooks/queries/useTopics";
import { useBusinessItemStore } from "@/stores/businessItemStore";
import { useBusinessSectionStore } from "@/stores/businessSectionStore";
import type {
  BusinessItem,
  BusinessItemDetail,
} from "@/types/BusinessSection.types";
import { DragDropContext, Droppable } from "@hello-pangea/dnd";
import { useEffect, useState } from "react";
import { Focusable } from "../common/Focusable";
import { BusinessItemRow } from "./components/BusinessItemRow";
import { LockedBanner } from "./components/LockedBanner";
import { NewRow } from "./components/NewRow";
import { BusinessItemsTableHeader } from "./components/TableHeader";

interface BusinessItemTableProps {
  itemDetails: BusinessItemDetail[];
  selectedBusinessItem: BusinessItem | null;
  onBackToItems: () => void;
  sectionId?: string;
  topicId?: string;
}

export function BusinessItemTable({
  itemDetails,
  selectedBusinessItem,
  sectionId,
  topicId,
}: BusinessItemTableProps) {
  const { updateTopicEntry, addTopicEntry, getTopicEntries, setTopicEntries } =
    useBusinessSectionStore();
  const { setSelectedItem } = useBusinessItemStore();
  const { sections } = useBusinessSectionStore();
  const projectId =
    typeof window !== "undefined"
      ? location.pathname.split("/projects/")[1]?.split("/")[0] || ""
      : "";
  const { createEntry, updateEntry } = useTopicEntryMutations(projectId);
  const { data: allTopics = [] } = useAllProjectTopics(projectId);

  // Check if the selected item is locked
  const isItemLocked = selectedBusinessItem
    ? !(selectedBusinessItem as any)?.isUnlocked
    : false;
  const lockedDependencies =
    (selectedBusinessItem as any)?.lockedDependencies || [];

  // Debug logging
  console.log("🔒 [BusinessItemTable] Locked dependencies:", {
    lockedDependencies,
    selectedBusinessItem: selectedBusinessItem?.title,
    selectedBusinessItemKeys: selectedBusinessItem
      ? Object.keys(selectedBusinessItem)
      : [],
  });

  // Local state for reordering
  const [localItemDetails, setLocalItemDetails] =
    useState<BusinessItemDetail[]>(itemDetails);
  const [editingCell, setEditingCell] = useState<{
    id: string;
    field: string;
  } | null>(null);
  const [newRowData, setNewRowData] = useState({
    id: "new-row",
    title: "",
    actions: "",
  });

  // Update local state when props change
  useEffect(() => {
    console.log("🔥 [BusinessItemTable] Props changed:", {
      itemDetailsCount: itemDetails.length,
      itemDetails: itemDetails.slice(0, 2),
      selectedBusinessItem: selectedBusinessItem?.title,
      sectionId,
      topicId,
    });
    setLocalItemDetails(itemDetails);
  }, [itemDetails, sectionId, topicId]);

  // Determine overall entry status from field values and actions progress
  const parseActionsSafe = (
    actions: string
  ): Array<{ progress?: number; results?: Array<{ content?: string }> }> => {
    if (!actions || typeof actions !== "string") return [];
    try {
      const parsed = JSON.parse(actions);
      const items = Array.isArray(parsed)
        ? parsed
        : Array.isArray((parsed as any)?.actions)
        ? (parsed as any).actions
        : [];
      return Array.isArray(items) ? items : [];
    } catch {
      return [];
    }
  };

  const isActionsProgressComplete = (actions: string): boolean => {
    const items = parseActionsSafe(actions);
    return items.some((a) => Number(a?.progress) === 100);
  };

  const hasAnyResultInActions = (actions: string): boolean => {
    const items = parseActionsSafe(actions);
    return items.some((a) => (a.results?.[0]?.content || "").trim().length > 0);
  };

  const determineStatus = (
    title: string,
    actions: string,
    result: string
  ): BusinessItemDetail["status"] => {
    const hasTitle = Boolean(title?.trim());
    const hasAction =
      Boolean(actions?.trim()) || parseActionsSafe(actions).length > 0;
    const hasResult = Boolean(result?.trim()) || hasAnyResultInActions(actions);

    if (hasResult) {
      return isActionsProgressComplete(actions) ? "confirmed" : "unproven";
    }
    if (hasAction) return "action";
    if (hasTitle) return "idea";
    return "idea";
  };

  const handleSave = (
    id: string,
    field: keyof BusinessItemDetail,
    value: string
  ) => {
    if (id === "new-row") {
      // Handle new row creation
      const updatedNewRowData = { ...newRowData, [field]: value };

      // Generate temporary ID for local state, backend will provide real ID
      const tempId = `temp-${Date.now()}`;
      const newItem: BusinessItemDetail = {
        id: tempId,
        title: field === "title" ? value : newRowData.title,
        actions: field === "actions" ? value : newRowData.actions,
        result: "",
        status: determineStatus(
          field === "title" ? value : newRowData.title,
          field === "actions" ? value : newRowData.actions,
          ""
        ),
        description: "",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // Update the specific field that was edited
      newItem[field as keyof BusinessItemDetail] = value as any;

      // Only create if there's actual content
      if (value.trim() !== "") {
        const updatedItems = [...localItemDetails, newItem];
        setLocalItemDetails(updatedItems);

        // Update store with new entry
        if (sectionId && topicId) {
          console.log("🔥 [BusinessItemTable] Adding new entry to store:", {
            sectionId,
            topicId,
            newItem,
          });
          addTopicEntry(sectionId, topicId, newItem);
          // Persist to backend
          try {
            const payload = {
              idea: newItem.title,
              action: newItem.actions,
              result: "",
              status: newItem.status,
              position: 0,
              metadata: {},
            };
            console.log("🔥 [BusinessItemTable] createEntry.mutate →", {
              topicId,
              payload,
            });
            createEntry.mutate(
              {
                topicId: topicId!,
                payload,
              },
              {
                onSuccess: (data: any) => {
                  console.log(
                    "🔥 [BusinessItemTable] createEntry success:",
                    data
                  );
                  // Update local state with real ID from backend
                  if (data?.id) {
                    const updatedItem = { ...newItem, id: String(data.id) };
                    setLocalItemDetails((prev) =>
                      prev.map((item) =>
                        item.id === tempId ? updatedItem : item
                      )
                    );
                    // Update store with real ID
                    updateTopicEntry(sectionId, topicId, tempId, {
                      id: String(data.id),
                    });
                  }
                },
                onError: (error: any) => {
                  console.error(
                    "❌ [BusinessItemTable] createEntry error:",
                    error
                  );
                  // Remove the temporary item on error
                  setLocalItemDetails((prev) =>
                    prev.filter((item) => item.id !== tempId)
                  );
                },
              }
            );
          } catch (e) {
            console.error("❌ [BusinessItemTable] createEntry.mutate error", e);
          }
        } else {
          console.error(
            "❌ [BusinessItemTable] Missing sectionId or topicId for new entry:",
            { sectionId, topicId }
          );
        }

        // Reset new row data
        setNewRowData({
          id: "new-row",
          title: "",
          actions: "",
        });
      } else {
        // Just update the new row data for display
        setNewRowData(updatedNewRowData);
      }
    } else {
      // Handle existing item update
      const item = localItemDetails.find((item) => item.id === id);
      if (item) {
        const updatedItem = { ...item, [field]: value };

        // Normalize/derive status
        if (field === "status") {
          const incoming = String(value);
          if (incoming === "result") {
            updatedItem.status = isActionsProgressComplete(updatedItem.actions)
              ? "confirmed"
              : "unproven";
          } else if (
            incoming === "idea" ||
            incoming === "action" ||
            incoming === "unproven" ||
            incoming === "confirmed"
          ) {
            updatedItem.status = incoming as BusinessItemDetail["status"];
          } else {
            updatedItem.status = determineStatus(
              updatedItem.title,
              updatedItem.actions,
              updatedItem.result
            );
          }
        } else if (
          field === "title" ||
          field === "actions" ||
          field === "result"
        ) {
          updatedItem.status = determineStatus(
            updatedItem.title,
            updatedItem.actions,
            updatedItem.result
          );
        }

        const updatedItems = localItemDetails.map((existingItem) =>
          existingItem.id === id ? updatedItem : existingItem
        );
        setLocalItemDetails(updatedItems);

        // Update store with changed entry
        if (sectionId && topicId) {
          console.log("🔥 [BusinessItemTable] Updating entry in store:", {
            sectionId,
            topicId,
            id,
            field,
            value,
          });
          updateTopicEntry(sectionId, topicId, id, {
            [field]: value,
            status: updatedItem.status,
          });
          // Only persist to backend if this is a real backend entry (numeric ID)
          if (!id.startsWith("temp-")) {
            try {
              const payload = {
                idea: updatedItem.title,
                action: updatedItem.actions,
                result: updatedItem.result || "",
                status: updatedItem.status,
                position: 0,
                metadata: {},
              } as const;
              console.log("🔥 [BusinessItemTable] updateEntry.mutate →", {
                topicId,
                entryId: id,
                payload,
              });
              updateEntry.mutate({ topicId: topicId!, entryId: id, payload });
            } catch (e) {
              console.error(
                "❌ [BusinessItemTable] updateEntry.mutate error",
                e
              );
            }
          } else {
            console.log(
              "🔄 [BusinessItemTable] Skipping backend update for temporary entry:",
              id
            );
          }
        } else {
          console.error(
            "❌ [BusinessItemTable] Missing sectionId or topicId for entry update:",
            { sectionId, topicId }
          );
        }
      }
    }
    setEditingCell(null);
  };

  const handleDragEnd = (result: any) => {
    // Check if the drop was outside the droppable area
    if (!result.destination) {
      return;
    }

    const { source, destination } = result;

    // If dropped in the same position, do nothing
    if (source.index === destination.index) {
      return;
    }

    // Reorder items
    const newItems = Array.from(localItemDetails);
    const [reorderedItem] = newItems.splice(source.index, 1);
    newItems.splice(destination.index, 0, reorderedItem);

    setLocalItemDetails(newItems);
  };

  return (
    <TooltipProvider>
      <div className="w-full max-w-full overflow-hidden">
        <LockedBanner
          isItemLocked={isItemLocked}
          lockedDependencies={lockedDependencies}
          projectId={projectId}
          selectedBusinessItem={selectedBusinessItem}
          sections={sections}
          getTopicEntries={getTopicEntries}
          setTopicEntries={setTopicEntries}
          setSelectedItem={setSelectedItem as any}
          allTopics={allTopics as any}
        />

        <Focusable focusKey="siftt" level={2}>
          {({ isFocused }) => (
            <Card className="w-full h-full p-0 overflow-hidden rounded-none border-0 relative z-50">
              <CardContent className="p-0 h-full">
                <DragDropContext onDragEnd={handleDragEnd}>
                  <div className="w-full overflow-auto h-full border-b border-[var(--siift-light-mid)]/50 relative z-40">
                    <Table className="relative z-30 table-fixed">
                      <colgroup>
                        <col style={{ width: "3rem" }} />
                        <col style={{ width: "calc((100% - 3rem) / 3)" }} />
                        <col style={{ width: "calc((100% - 3rem) / 3)" }} />
                        <col style={{ width: "calc((100% - 3rem) / 3)" }} />
                      </colgroup>
                      <BusinessItemsTableHeader />

                      <Droppable droppableId="business-items">
                        {(provided) => (
                          <TableBody
                            {...provided.droppableProps}
                            ref={provided.innerRef}
                          >
                            {localItemDetails.map((detail, index) => (
                              <BusinessItemRow
                                key={detail.id}
                                detail={detail}
                                index={index}
                                editingCell={editingCell}
                                setEditingCell={setEditingCell}
                                onSave={handleSave}
                                isItemLocked={isItemLocked}
                              />
                            ))}

                            <NewRow
                              newRowData={newRowData}
                              editingCell={editingCell}
                              setEditingCell={setEditingCell}
                              onSave={handleSave}
                              isItemLocked={isItemLocked}
                            />

                            {provided.placeholder}
                          </TableBody>
                        )}
                      </Droppable>
                    </Table>
                  </div>
                </DragDropContext>
              </CardContent>
            </Card>
          )}
        </Focusable>
      </div>
    </TooltipProvider>
  );
}

export default BusinessItemTable;
