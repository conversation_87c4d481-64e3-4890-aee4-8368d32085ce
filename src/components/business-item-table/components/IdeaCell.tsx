"use client";

import type { BusinessItemDetail } from "@/types/BusinessSection.types";
import { EditableCell } from "../../EditableCell";

interface IdeaCellProps {
  detail: BusinessItemDetail;
  editingCell: { id: string; field: string } | null;
  setEditingCell: (cell: { id: string; field: string } | null) => void;
  onSave: (id: string, field: keyof BusinessItemDetail, value: string) => void;
}

export function IdeaCell({
  detail,
  editingCell,
  setEditingCell,
  onSave,
}: IdeaCellProps) {
  const isEditingTitle =
    editingCell?.id === detail.id && editingCell?.field === "title";
  return (
    <div className="flex flex-col justify-between h-full ">
      <div
        className={`flex-1 p-2 border ${
          isEditingTitle
            ? "border-[var(--ring)] border-2 "
            : "border-transparent"
        }`}
      >
        <EditableCell
          id={detail.id}
          field="title"
          value={detail.title}
          multiline={true}
          className="h-full"
          fillContainer={true}
          editingCell={editingCell}
          setEditingCell={setEditingCell}
          onSave={onSave}
        />
      </div>

      <div className="border-t border-[var(--siift-light-mid)]/50">
        {(() => {
          // Compute average progress across all actions for this entry
          const parseActions = (
            actions: string
          ): Array<{
            progress?: number;
            results?: Array<{ content?: string }>;
          }> => {
            if (!actions || typeof actions !== "string") return [];
            try {
              const parsed = JSON.parse(actions as string);
              const items = Array.isArray(parsed)
                ? parsed
                : Array.isArray((parsed as any)?.actions)
                ? (parsed as any).actions
                : [];
              return Array.isArray(items) ? items : [];
            } catch {
              return [];
            }
          };

          const items = parseActions(detail.actions || "");
          // Only count actions that have a non-empty result; others contribute 0
          const progressValues = items.map((a) => {
            const hasNonEmptyResult = Boolean(
              (a?.results?.[0]?.content || "").trim()
            );
            const p = Number(a?.progress) || 0;
            return hasNonEmptyResult ? p : 0;
          });
          const avg =
            progressValues.length > 0
              ? Math.round(
                  progressValues.reduce((s, v) => s + v, 0) /
                    progressValues.length
                )
              : 0;
          return (
            <div className="w-full flex items-center gap-4 p-2">
              <div className="text-xs text-[var(--text-muted)]/50 whitespace-nowrap">
                Validation
              </div>
              <div className="w-full h-2 rounded-sm border border-[var(--progress-border)]/30 bg-[var(--progress-bg)]/30 relative overflow-hidden">
                {/* Progress fill with striped pattern */}
                <div
                  className="h-full rounded-sm bg-[var(--progress-fill)] transition-all duration-300 relative"
                  style={{
                    width: `${avg}%`,
                  }}
                ></div>
                <div
                  className="absolute inset-0 opacity-30"
                  style={{
                    backgroundImage: `repeating-linear-gradient(
                    45deg,
                    transparent,
                    transparent 1.5px,
                    rgba(156, 163, 175, 0.4) 1.5px,
                    rgba(156, 163, 175, 0.4) 3px
                  )`,
                  }}
                />
              </div>
            </div>
          );
        })()}
      </div>
    </div>
  );
}
