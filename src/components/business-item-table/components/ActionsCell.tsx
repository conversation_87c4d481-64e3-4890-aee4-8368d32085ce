"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import type { BusinessItemDetail } from "@/types/BusinessSection.types";
import { AlertCircle, CheckCircle, Circle, Clock } from "lucide-react";
import { useEffect, useRef, useState } from "react";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>etHeader,
  <PERSON>etTitle,
} from "@/components/ui/sheet";
import { Textarea } from "@/components/ui/textarea";
import { SidebarButton } from "../../ui/sidebar-button";

// Action types
type ActionType = "manual" | "deep-research" | "web-search" | "dataset-search";

// Progress status types
type ProgressStatus = "idea" | "action" | "result" | "confirmed";

// Result object structure
interface ActionResult {
  id: string;
  content: string;
  timestamp: string;
  type: "text" | "link" | "file";
}

// Action object structure
interface ActionItem {
  id: string;
  type: ActionType;
  content: string;
  credit: number;
  progress: number; // 0-100 percentage
  status: ProgressStatus;
  results: ActionResult[];
}

// Parse actions from JSON string or create default structure
function parseActions(actionsString: string): ActionItem[] {
  if (!actionsString?.trim()) return [];

  try {
    const parsed = JSON.parse(actionsString);
    if (Array.isArray(parsed)) {
      return parsed.map((item) => ({
        // Use a deterministic id when missing to keep UI overrides stable between renders
        id: String(
          item.id ??
            item._id ??
            `${item.type || "manual"}|${item.content || ""}|${item.credit ?? 0}`
        ),
        type: item.type || "manual",
        content: item.content || "",
        credit: item.credit || 1,
        progress: item.progress || 0,
        status: item.status || "idea",
        results: item.results || [],
      }));
    }
  } catch (e) {
    // If not valid JSON, treat as legacy text format
    return actionsString
      .split("\n")
      .filter((item) => item.trim())
      .map((item) => ({
        // Stable id based on content for legacy entries
        id: `legacy|${item.trim()}`,
        type: "manual" as ActionType,
        content: item.trim(),
        credit: 1,
        progress: 0,
        status: "idea" as ProgressStatus,
        results: [],
      }));
  }

  return [];
}

// Convert actions back to JSON string for storage
function actionsToJson(actions: ActionItem[]): string {
  return JSON.stringify(actions);
}

interface ActionsCellProps {
  detail: BusinessItemDetail;
  disabled?: boolean;
  onSave: (id: string, field: keyof BusinessItemDetail, value: string) => void;
}

export function ActionsCell({ detail, disabled, onSave }: ActionsCellProps) {
  // Progress management state

  // Progress management state
  const [progressOpen, setProgressOpen] = useState(false);
  const [selectedAction, setSelectedAction] = useState<ActionItem | null>(null);
  const [actionSheetOpen, setActionSheetOpen] = useState(false);
  const [sidebarActionDraft, setSidebarActionDraft] = useState<string>("");
  const [sidebarResultDraft, setSidebarResultDraft] = useState<string>("");
  const sidebarSavedRef = useRef(false);

  const [progressOverrides, setProgressOverrides] = useState<
    Record<string, number>
  >({});
  const saveTimersRef = useRef<Record<string, ReturnType<typeof setTimeout>>>(
    {}
  );

  // Always layer overrides onto parsed actions so UI shows latest local value until server echoes
  const parsedActions = parseActions(detail.actions || "");
  const actionItems = parsedActions.map((a) => ({
    ...a,
    progress: progressOverrides[a.id] ?? a.progress,
  }));
  // Reconcile overrides after props update: clear override when server/store reflects it
  useEffect(() => {
    const parsed = parseActions(detail.actions || "");
    setProgressOverrides((prev) => {
      let changed = false;
      const next = { ...prev };
      parsed.forEach((a) => {
        const ov = next[a.id];
        if (ov !== undefined && Number(a.progress) === Number(ov)) {
          delete next[a.id];
          changed = true;
        }
      });
      return changed ? next : prev;
    });
  }, [detail.actions]);

  const getActionProgress = (action: ActionItem) =>
    progressOverrides[action.id] ?? action.progress;

  const computeEntryStatusFromActions = (
    actions: ActionItem[]
  ): ProgressStatus => {
    const hasAnyAction = actions.length > 0;
    const anyHasResult = actions.some((a) => (a.results?.length || 0) > 0);
    const anyProgress100 = actions.some((a) => Number(a.progress) === 100);
    if (anyHasResult) {
      return anyProgress100 ? "confirmed" : "result";
    }
    return hasAnyAction ? "action" : "idea";
  };

  const handleProgressSave = () => {
    if (selectedAction) {
      const updatedActions = actionItems.map((item) =>
        item.id === selectedAction.id ? selectedAction : item
      );
      onSave(detail.id, "actions", actionsToJson(updatedActions));
    }
    setProgressOpen(false);
  };

  // Debounced result editing
  const [resultBuffers, setResultBuffers] = useState<Record<string, string>>(
    {}
  );
  const resultTimersRef = useRef<Record<string, ReturnType<typeof setTimeout>>>(
    {}
  );

  const scheduleResultSave = (
    actionId: string,
    resultId: string,
    content: string
  ) => {
    setResultBuffers((prev) => ({ ...prev, [actionId]: content }));
    const existing = resultTimersRef.current[actionId];
    if (existing) clearTimeout(existing);
    resultTimersRef.current[actionId] = setTimeout(() => {
      const latestBuffer = resultBuffers[actionId];
      const latest = (
        latestBuffer !== undefined ? latestBuffer : content
      ).trim();
      handleUpdateResult(actionId, resultId, latest);
      // don't clear resultBuffers here; wait for server echo and effect to reconcile
      delete resultTimersRef.current[actionId];
    }, 400);
  };

  // Flush buffered result text after a result object gets created (first keystroke path)
  useEffect(() => {
    const actions = actionItems;
    actions.forEach((a) => {
      const buffered = resultBuffers[a.id];
      if (buffered !== undefined && a.results && a.results[0]) {
        const r = a.results[0];
        const bufferedTrim = (buffered || "").trim();
        const serverTrim = (r.content || "").trim();
        if (serverTrim === bufferedTrim) {
          // server caught up → clear buffer for this action
          setResultBuffers((prev) => {
            const copy = { ...prev };
            delete copy[a.id];
            return copy;
          });
        } else if (serverTrim === "" && bufferedTrim !== "") {
          // result object exists but empty → push buffered content
          scheduleResultSave(a.id, r.id, buffered);
        }
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [detail.actions]);

  const handleUpdateResult = (
    actionId: string,
    resultId: string,
    content: string
  ) => {
    const action = actionItems.find((item) => item.id === actionId);
    if (action) {
      const updatedResults = action.results.map((result) =>
        result.id === resultId ? { ...result, content } : result
      );

      // Update status based on content and progress
      let newStatus: ProgressStatus;
      if (content.trim()) {
        newStatus = action.progress === 100 ? "confirmed" : "result";
      } else {
        newStatus = "action";
      }

      const updatedAction: ActionItem = {
        ...action,
        results: updatedResults,
        status: newStatus,
        progress: content.trim() ? action.progress : 0,
      };
      console.log("Updating result, new status:", newStatus);
      const updatedActions: ActionItem[] = actionItems.map((item) =>
        item.id === actionId ? updatedAction : item
      );
      console.log("Saving updated actions to backend:", updatedActions);
      console.log("JSON payload being sent:", actionsToJson(updatedActions));

      // Save both actions and update the business item status
      onSave(detail.id, "actions", actionsToJson(updatedActions));
      onSave(
        detail.id,
        "status",
        computeEntryStatusFromActions(updatedActions)
      );
    }
  };

  return (
    <div className={disabled ? "opacity-50 pointer-events-none" : ""}>
      {/* Action Items */}
      {actionItems.length > 0 && (
        <div className="space-y-2">
          {actionItems.map((action) => {
            const hasResult = action.results.length > 0;
            const result = hasResult ? action.results[0] : null;
            const hasNonEmptyResult = Boolean(result?.content?.trim());
            const displayProgress = hasNonEmptyResult
              ? progressOverrides[action.id] ?? getActionProgress(action)
              : 0;

            return (
              <div
                key={action.id}
                role="button"
                onClick={() => {
                  setSelectedAction(action);
                  setSidebarActionDraft(action.content || "");
                  const r0 = (action.results && action.results[0]) || null;
                  setSidebarResultDraft(r0?.content || "");
                  sidebarSavedRef.current = false;
                  setActionSheetOpen(true);
                }}
                className="rounded border border-[var(--siift-light-mid)]/50  bg-[var(--background)] px-2 py-2 hover:bg-[var(--accent)]/10  hover:border-[var(--accent)] cursor-pointer transition-colors"
              >
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-xs font-medium text-[var(--muted-foreground)]">
                      {action.type.toUpperCase()}
                    </span>
                    <span
                      className={`text-xs ${
                        displayProgress > 90
                          ? " text-[var(--primary)]"
                          : displayProgress > 50
                          ? " text-[var(--siift-darkest)]"
                          : "text-[var(--destructive)]"
                      }`}
                    >
                      {displayProgress}%
                    </span>
                  </div>
                  <p className=" text-sm leading-tight text-[var(--foreground)] line-clamp-2 break-words">
                    {action.content}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Manual Action creation moved to Suggestions column */}

      {/* Progress Management Sheet */}
      <Sheet open={progressOpen} onOpenChange={setProgressOpen}>
        <SheetContent
          side="bottom"
          className="max-w-[50vw] mx-auto rounded-t-xl p-6"
        >
          <SheetHeader className="text-center">
            <SheetTitle>Action Status</SheetTitle>
          </SheetHeader>

          <div className="py-4 space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Status</label>
              <Select
                value={selectedAction?.status || "idea"}
                onValueChange={(value: ProgressStatus) =>
                  setSelectedAction((prev) =>
                    prev ? { ...prev, status: value } : null
                  )
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="idea" className="flex items-center gap-2">
                    <Circle className="h-4 w-4" />
                    Idea
                  </SelectItem>
                  <SelectItem
                    value="action"
                    className="flex items-center gap-2"
                  >
                    <Clock className="h-4 w-4" />
                    Action
                  </SelectItem>
                  <SelectItem
                    value="result"
                    className="flex items-center gap-2"
                  >
                    <CheckCircle className="h-4 w-4" />
                    Result
                  </SelectItem>
                  <SelectItem
                    value="confirmed"
                    className="flex items-center gap-2"
                  >
                    <AlertCircle className="h-4 w-4" />
                    Confirmed
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <SheetFooter>
            <div className="flex gap-3 w-full">
              <Button
                variant="outline"
                onClick={() => setProgressOpen(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button onClick={handleProgressSave} className="flex-1">
                Save Progress
              </Button>
            </div>
          </SheetFooter>
        </SheetContent>
      </Sheet>

      {/* New Action creation moved to Suggestions column */}

      {/* Action Details Sheet */}
      <Sheet
        open={actionSheetOpen}
        onOpenChange={(open) => {
          if (!open && selectedAction) {
            // Commit on close if there are changes and not already saved
            try {
              const current =
                actionItems.find((a) => a.id === selectedAction.id) ||
                selectedAction;
              const currentResult =
                (current.results && current.results[0]) || null;
              const hasActionChanged =
                (sidebarActionDraft ?? "") !== (current.content ?? "");
              const hasResultChanged =
                (sidebarResultDraft ?? "") !== (currentResult?.content ?? "");
              if (
                !sidebarSavedRef.current &&
                (hasActionChanged || hasResultChanged)
              ) {
                const updatedAction: ActionItem = {
                  ...current,
                  content: sidebarActionDraft ?? "",
                  results: currentResult
                    ? [{ ...currentResult, content: sidebarResultDraft ?? "" }]
                    : ([
                        {
                          id: crypto.randomUUID(),
                          content: sidebarResultDraft ?? "",
                          timestamp: new Date().toISOString(),
                          type: "text",
                        },
                      ] as any),
                } as ActionItem;
                // Update status/progress based on result content
                const hasNonEmpty = Boolean((sidebarResultDraft ?? "").trim());
                const nextProgress = hasNonEmpty ? updatedAction.progress : 0;
                const nextStatus = hasNonEmpty
                  ? nextProgress === 100
                    ? "confirmed"
                    : "result"
                  : "action";
                const finalAction: ActionItem = {
                  ...updatedAction,
                  progress: nextProgress,
                  status: nextStatus,
                };
                const updatedActions = actionItems.map((it) =>
                  it.id === finalAction.id ? finalAction : it
                );
                onSave(detail.id, "actions", actionsToJson(updatedActions));
              }
            } finally {
              setActionSheetOpen(false);
              setSelectedAction(null);
            }
          } else {
            setActionSheetOpen(open);
          }
        }}
      >
        <SheetContent side="right" className="w-[520px] max-w-[90vw] p-0">
          <SheetHeader>
            <SheetTitle className="text-md">Action Details</SheetTitle>
          </SheetHeader>

          {selectedAction &&
            (() => {
              const current =
                actionItems.find((a) => a.id === selectedAction.id) ||
                selectedAction;
              const currentResult =
                (current.results && current.results[0]) || null;
              const hasNonEmptyResult = Boolean(currentResult?.content?.trim());
              const hasResultOrDraft =
                hasNonEmptyResult || Boolean((sidebarResultDraft ?? "").trim());
              const displayProgress = hasResultOrDraft
                ? progressOverrides[current.id] ?? getActionProgress(current)
                : 0;
              const lastUpdatedIso =
                (currentResult && currentResult.timestamp) || detail.updatedAt;
              const lastUpdated = lastUpdatedIso
                ? new Date(lastUpdatedIso).toLocaleString()
                : null;

              return (
                <div className="space-y-6 px-4 pb-4">
                  {/* Action text */}
                  <div>
                    <label className="text-sm font-medium text-[var(--muted-foreground)] my-3 block">
                      Action
                    </label>
                    <Textarea
                      value={sidebarActionDraft}
                      onChange={(e) => setSidebarActionDraft(e.target.value)}
                      className="min-h-[100px] border border-[var(--siift-light-mid)]/50 rounded-md p-2"
                    />
                  </div>

                  {/* Result */}
                  <div>
                    <label className="text-sm font-medium text-[var(--muted-foreground)] my-3 block">
                      Result
                    </label>
                    <Textarea
                      value={sidebarResultDraft}
                      placeholder="Enter result..."
                      className="min-h-[100px] text-sm border border-[var(--siift-light-mid)]/50 rounded-md p-2"
                      onChange={(e) => {
                        const content = e.target.value;
                        setSidebarResultDraft(content);

                        // Debounced auto-save for result text
                        if (selectedAction) {
                          const currentResult =
                            (selectedAction.results &&
                              selectedAction.results[0]) ||
                            null;
                          if (currentResult) {
                            // Update existing result
                            scheduleResultSave(
                              selectedAction.id,
                              currentResult.id,
                              content
                            );
                          } else if (content.trim()) {
                            // Create new result if content is not empty
                            const newResultId = crypto.randomUUID();
                            const updatedAction: ActionItem = {
                              ...selectedAction,
                              results: [
                                {
                                  id: newResultId,
                                  content: content,
                                  timestamp: new Date().toISOString(),
                                  type: "text",
                                },
                              ],
                              status: "result",
                            };
                            const updatedActions = actionItems.map((item) =>
                              item.id === selectedAction.id
                                ? updatedAction
                                : item
                            );
                            onSave(
                              detail.id,
                              "actions",
                              actionsToJson(updatedActions)
                            );
                          }
                        }
                      }}
                    />
                  </div>

                  {/* Progress */}
                  {(hasNonEmptyResult || sidebarResultDraft.trim()) && (
                    <div>
                      <div className="flex items-center justify-between text-sm mb-1 text-[var(--text-muted)] ">
                        <span>Validation</span>
                        <span>{displayProgress}%</span>
                      </div>
                      <input
                        type="range"
                        min="0"
                        max="100"
                        value={progressOverrides[current.id] ?? displayProgress}
                        onChange={(e) => {
                          const newProgress = parseInt(e.target.value) || 0;
                          // Optimistically update the selectedAction so the UI reflects immediately
                          setSelectedAction((prev) =>
                            prev
                              ? {
                                  ...prev,
                                  progress: newProgress,
                                  status:
                                    (prev.results?.length || 0) > 0
                                      ? newProgress === 100
                                        ? "confirmed"
                                        : "result"
                                      : "action",
                                }
                              : prev
                          );
                          setProgressOverrides((prev) => ({
                            ...prev,
                            [current.id]: newProgress,
                          }));
                          const existingTimer =
                            saveTimersRef.current[current.id];
                          if (existingTimer) clearTimeout(existingTimer);
                          saveTimersRef.current[current.id] = setTimeout(() => {
                            const shouldCreateResult =
                              (current.results?.length || 0) === 0 &&
                              Boolean((sidebarResultDraft ?? "").trim());
                            const updatedAction = {
                              ...current,
                              results: shouldCreateResult
                                ? [
                                    {
                                      id: crypto.randomUUID(),
                                      content: (
                                        sidebarResultDraft ?? ""
                                      ).trim(),
                                      timestamp: new Date().toISOString(),
                                      type: "text",
                                    },
                                  ]
                                : current.results,
                              progress: newProgress,
                              status:
                                shouldCreateResult ||
                                (current.results?.length || 0) > 0
                                  ? newProgress === 100
                                    ? "confirmed"
                                    : "result"
                                  : "action",
                            } as ActionItem;
                            const updatedActions = actionItems.map((item) =>
                              item.id === current.id ? updatedAction : item
                            );
                            onSave(
                              detail.id,
                              "actions",
                              actionsToJson(updatedActions)
                            );
                          }, 400);
                        }}
                        className="w-full h-2 bg-[var(--siift-light-mid)] rounded-full appearance-none cursor-pointer slider"
                        style={{
                          background: `linear-gradient(to right, var(--primary) 0%, var(--primary) ${displayProgress}%, var(--siift-light-mid) ${displayProgress}%, var(--siift-light-mid) 100%)`,
                        }}
                      />
                      <style jsx>{`
                        .slider::-webkit-slider-thumb {
                          appearance: none;
                          height: 16px;
                          width: 16px;
                          border-radius: 50%;
                          background: var(--primary);
                          cursor: pointer;
                          border: 2px solid var(--background);
                          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                        }
                        .slider::-moz-range-thumb {
                          height: 16px;
                          width: 16px;
                          border-radius: 50%;
                          background: var(--primary);
                          cursor: pointer;
                          border: 2px solid var(--background);
                          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                        }
                      `}</style>
                    </div>
                  )}

                  <div className="flex items-center justify-between gap-2 pt-2">
                    {lastUpdated && (
                      <div className="text-xs text-[var(--muted-foreground)]">
                        Last updated {lastUpdated}
                      </div>
                    )}
                    <div className="text-xs text-[var(--muted-foreground)]"></div>
                    <div className="flex gap-2">
                      <SidebarButton
                        variant="outline"
                        onClick={() => {
                          sidebarSavedRef.current = true;
                          setActionSheetOpen(false);
                          setSelectedAction(null);
                        }}
                      >
                        Cancel
                      </SidebarButton>
                      <SidebarButton
                        variant="outline"
                        onClick={() => {
                          if (!selectedAction) return;
                          const current =
                            actionItems.find(
                              (a) => a.id === selectedAction.id
                            ) || selectedAction;
                          const currentResult =
                            (current.results && current.results[0]) || null;
                          const updatedAction: ActionItem = {
                            ...current,
                            content: sidebarActionDraft ?? "",
                            results: currentResult
                              ? [
                                  {
                                    ...currentResult,
                                    content: sidebarResultDraft ?? "",
                                  },
                                ]
                              : ([
                                  {
                                    id: crypto.randomUUID(),
                                    content: sidebarResultDraft ?? "",
                                    timestamp: new Date().toISOString(),
                                    type: "text",
                                  },
                                ] as any),
                          } as ActionItem;
                          const hasNonEmpty = Boolean(
                            (sidebarResultDraft ?? "").trim()
                          );
                          const nextProgress = hasNonEmpty
                            ? updatedAction.progress
                            : 0;
                          const nextStatus = hasNonEmpty
                            ? nextProgress === 100
                              ? "confirmed"
                              : "result"
                            : "action";
                          const finalAction: ActionItem = {
                            ...updatedAction,
                            progress: nextProgress,
                            status: nextStatus,
                          };
                          const updatedActions = actionItems.map((it) =>
                            it.id === finalAction.id ? finalAction : it
                          );
                          onSave(
                            detail.id,
                            "actions",
                            actionsToJson(updatedActions)
                          );
                          sidebarSavedRef.current = true;
                          setActionSheetOpen(false);
                          setSelectedAction(null);
                        }}
                      >
                        Save
                      </SidebarButton>
                    </div>
                  </div>
                </div>
              );
            })()}
        </SheetContent>
      </Sheet>
    </div>
  );
}
