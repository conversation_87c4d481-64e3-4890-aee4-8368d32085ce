"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { SidebarButton } from "@/components/ui/sidebar-button";
import { memo, useMemo, useState } from "react";
import { Badge } from "@/components/ui/badge";
import {
  Lightbulb,
  Zap,
  Check,
  AlertTriangle,
  Users,
  BarChart3,
  TrendingUp,
  Star,
  Package,
  Settings,
  Target,
  Megaphone,
  Palette,
  MessageCircle,
  FileText,
  Building,
  DollarSign,
  Shield,
  Heart,
  Maximize2,
} from "lucide-react";
import type {
  ProjectTopicSummary,
  TopicEntry,
} from "@/hooks/queries/useTopics";
import { Logo } from "../ui/logo";
import { useBusinessSectionStore } from "@/stores/businessSectionStore";
import { useBusinessItemStore } from "@/stores/businessItemStore";
import type {
  BusinessItem,
  BusinessItemDetail,
} from "@/types/BusinessSection.types";
import {
  Sheet,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { ScrollArea } from "@/components/ui/scroll-area";
import { computeEntryCounts } from "@/utils/entryCounts";
import { StatusCountBadge } from "../ui/status-count-badge";

// Safely normalize any value to a short display string
const toText = (value: unknown): string => {
  if (value == null) return "";
  if (typeof value === "string") return value;
  if (typeof value === "number" || typeof value === "boolean")
    return String(value);
  if (Array.isArray(value))
    return value
      .map((v) => toText(v))
      .filter(Boolean)
      .join(" ");
  if (typeof value === "object") {
    const v: any = value;
    const candidate = [
      v.title,
      v.idea,
      v.action,
      v.result,
      v.text,
      v.content,
    ].find((x) => typeof x === "string" && x.trim().length > 0);
    return candidate ? String(candidate) : ""; // avoid JSON/stringifying objects into UI
  }
  return "";
};

// Topic icon mapping based on topic titles
const getTopicIcon = (title: string) => {
  const titleLower = title.toLowerCase();
  if (titleLower.includes("problem")) return AlertTriangle;
  if (titleLower.includes("audience") || titleLower.includes("customer"))
    return Users;
  if (titleLower.includes("alternative") || titleLower.includes("competitor"))
    return Lightbulb;
  if (titleLower.includes("market") || titleLower.includes("size"))
    return BarChart3;
  if (titleLower.includes("trend")) return TrendingUp;
  if (titleLower.includes("value") || titleLower.includes("uvp")) return Star;
  if (titleLower.includes("product") || titleLower.includes("feature"))
    return Package;
  if (titleLower.includes("tech") || titleLower.includes("technology"))
    return Settings;
  if (titleLower.includes("positioning")) return Target;
  if (titleLower.includes("marketing") || titleLower.includes("campaign"))
    return Megaphone;
  if (titleLower.includes("brand") || titleLower.includes("design"))
    return Palette;
  if (titleLower.includes("communication") || titleLower.includes("message"))
    return MessageCircle;
  if (titleLower.includes("content") || titleLower.includes("documentation"))
    return FileText;
  if (titleLower.includes("structure") || titleLower.includes("organization"))
    return Building;
  if (
    titleLower.includes("revenue") ||
    titleLower.includes("monetization") ||
    titleLower.includes("pricing")
  )
    return DollarSign;
  if (titleLower.includes("legal") || titleLower.includes("compliance"))
    return Shield;
  if (titleLower.includes("culture") || titleLower.includes("values"))
    return Heart;
  return Lightbulb; // Default fallback
};

export const PrioritiesDropdown = memo(function PrioritiesDropdown({
  allTopics,
  entriesByTopic,
  isLoading = false,
}: {
  allTopics?: ProjectTopicSummary[];
  entriesByTopic?: Record<string, TopicEntry[]>;
  isLoading?: boolean;
}) {
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const { getTopicEntries, sections } = useBusinessSectionStore();
  const { setSelectedItem } = useBusinessItemStore();

  // Build actual priorities from data when available
  const computedItems = useMemo(() => {
    if (!allTopics || !entriesByTopic || allTopics.length === 0) return [];

    const items: {
      id: string;
      title: string;
      status: string;
      topic: string;
      topicId: string;
      sectionId: string;
      numericTopicId: number;
      entries: BusinessItemDetail[];
      topicIcon: any;
    }[] = [];

    const seen = new Set<string>();

    allTopics.forEach((topic) => {
      const entries = entriesByTopic[topic.topicId] || [];
      const storeEntries = getTopicEntries(
        topic.mappedCategoryId ?? topic.sectionId,
        String(topic.numericTopicId)
      ) as unknown as BusinessItemDetail[];

      // Normalize store entries
      const normalizedStoreEntries: BusinessItemDetail[] = (
        storeEntries || []
      ).map((e: any) => ({
        id: String(e.id),
        title:
          toText(e.title) ||
          toText(e.action) ||
          toText(e.idea) ||
          toText(e.result),
        actions: toText((e as any).actions) || toText((e as any).action),
        result: toText((e as any).result),
        status: (e.status as any) || "idea",
      }));

      const normalizedBackendEntries: BusinessItemDetail[] = (
        entries || []
      ).map((e) => ({
        id: String(e.id),
        title:
          toText((e as any).title) ||
          toText((e as any).action) ||
          toText((e as any).idea) ||
          toText((e as any).result),
        actions: toText((e as any).action),
        result: toText((e as any).result),
        status: (e.status as any) || "idea",
      }));

      // Merge by id, prefer store version
      const map = new Map<string, BusinessItemDetail>();
      normalizedBackendEntries.forEach((e) => map.set(e.id, e));
      normalizedStoreEntries.forEach((e) => map.set(e.id, e));
      const uniqueEntries = Array.from(map.values());

      if (uniqueEntries.length === 0) return;

      // Create an item for each entry
      uniqueEntries.forEach((entry) => {
        const key = `${topic.topicId}-${entry.id}`;
        if (seen.has(key)) return;
        seen.add(key);

        const status = entry.status || "idea";
        const topicIcon = getTopicIcon(topic.mappedLabel || topic.title);

        if (status === "unproven" || status === "confirmed") {
          return;
        }

        const title = (
          toText((entry as any).title) ||
          toText((entry as any).content) ||
          toText((entry as any).action) ||
          toText((entry as any).idea) ||
          toText((entry as any).result) ||
          ""
        ).trim();
        if (!title) return;

        items.push({
          id: key,
          title: title || `${topic.mappedLabel || topic.title} entry`,
          status,
          topic: topic.mappedLabel || topic.title,
          topicId: topic.topicId,
          sectionId: topic.mappedCategoryId ?? topic.sectionId,
          numericTopicId: topic.numericTopicId,
          entries: [entry],
          topicIcon,
        });
      });
    });

    // Sort: actions first, then results, then ideas, then unproven
    const order = { action: 0, confirmed: 1, idea: 2, unproven: 3 } as Record<
      string,
      number
    >;
    items.sort((a, b) => (order[a.status] ?? 99) - (order[b.status] ?? 99));

    return items;
  }, [allTopics, entriesByTopic, getTopicEntries, sections]);

  // Limit to top N
  const displayedItems = useMemo(
    () => computedItems.slice(0, 5),
    [computedItems]
  );
  const totalCount = displayedItems.length;
  const allCount = computedItems.length;

  // Handle entry click - navigate to parent topic
  const handleEntryClick = (item: any) => {
    // Build full entries list for the parent topic (merge backend + store)
    const toDetail = (e: any): BusinessItemDetail => ({
      id: String(e.id),
      title: (
        toText(e.title) ||
        toText((e as any).content) ||
        toText(e.action) ||
        toText(e.idea) ||
        toText(e.result)
      ).trim(),
      actions: toText((e as any).actions) || toText((e as any).action),
      result: toText((e as any).result),
      status: (e.status as any) || "idea",
    });

    const backendEntriesRaw = (entriesByTopic?.[item.topicId] ?? []) as any[];
    const backendEntries = backendEntriesRaw.map(toDetail);
    const storeEntriesRaw = getTopicEntries(
      item.sectionId,
      String(item.numericTopicId)
    ) as any[];
    const storeEntries = (storeEntriesRaw || []).map(toDetail);

    const mergedMap = new Map<string, BusinessItemDetail>();
    backendEntries.forEach((e) => mergedMap.set(e.id, e));
    storeEntries.forEach((e) => mergedMap.set(e.id, e));
    const mergedEntries = Array.from(mergedMap.values());

    const businessItem: BusinessItem & {
      entries?: BusinessItemDetail[];
      sectionId?: string;
      isUnlocked?: boolean;
      lockedDependencies?: string[];
    } = {
      id: String(item.numericTopicId),
      title: item.topic,
      status:
        (mergedEntries[0]?.status as any) || (item.status as any) || "idea",
      ...(() => {
        const { actions, ideas, confirmed } = computeEntryCounts(
          mergedEntries as any[]
        );
        return { actions, ideas, results: confirmed };
      })(),
      icon: item.topicIcon,
      sectionId: item.sectionId,
      entries: mergedEntries,
      isUnlocked: true,
      lockedDependencies: [],
    };
    // Debug: inspect object sent from PrioritiesDropdown
    console.log("[PrioritiesDropdown] setSelectedItem()", {
      id: businessItem.id,
      title: businessItem.title,
      status: businessItem.status,
      counts: {
        ideas: businessItem.ideas,
        actions: businessItem.actions,
        results: (businessItem as any).results,
      },
      entriesCount: businessItem.entries?.length || 0,
    });
    setSelectedItem(businessItem);
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <SidebarButton
            text={isLoading ? "Loading..." : "Priorities"}
            badge={isLoading ? "" : (allCount || "")}
            variant="ghost"
            className="bg-[var(--accent)]/10"
            hoverScale={true}
            showBorder={true}
            hoverColor="grey"
            textClassName=" text-lg"
            disabled={isLoading}
          />
        </DropdownMenuTrigger>
        <DropdownMenuContent
          className="w-80 rounded-lg animate-in fade-in-0 zoom-in-95"
          align="end"
          side="bottom"
          sideOffset={4}
        >
          <DropdownMenuLabel className="text-muted-foreground text-sm">
            {totalCount === 0
              ? "No priorities yet"
              : `Top priorities (${totalCount})`}
          </DropdownMenuLabel>
          <div className="mt-1 divide-y divide-[var(--border)]">
            {displayedItems.length === 0 ? (
              <div className="p-4 text-center">
                <div className="text-muted-foreground text-sm">
                  {!allTopics || allTopics.length === 0
                    ? "No topics available"
                    : "No priorities yet"}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  {!allTopics || allTopics.length === 0
                    ? "Topics will appear as they are created for this project."
                    : "Add entries to topics to see them here."}
                </div>
              </div>
            ) : (
              displayedItems.map((item) => {
                const status = String(item.status || "idea").toLowerCase();
                const statusBadge = (() => {
                  if (status === "action") {
                    return <StatusCountBadge type="action" count={1} />;
                  }
                  if (status === "confirmed") {
                    return <StatusCountBadge type="confirmed" count={1} />;
                  }
                  return <StatusCountBadge type="idea" count={1} />;
                })();

                return (
                  <DropdownMenuItem
                    key={item.id}
                    className="flex items-start gap-2 p-3 rounded-md hover:bg-[var(--hover-muted)]/30 cursor-pointer"
                    onClick={() => handleEntryClick(item)}
                  >
                    {statusBadge}
                    <div className="flex flex-col flex-1">
                      <span className="text-sm text-foreground font-medium line-clamp-2">
                        {toText(item.title).trim()}
                      </span>
                      <div className="flex items-center gap-1 ">
                        <span className="text-[10px] text-muted-foreground">
                          {item.topic}
                        </span>
                      </div>
                    </div>
                  </DropdownMenuItem>
                );
              })
            )}
          </div>
          {allCount > 5 ? (
            <div className="p-1">
              <DropdownMenuItem
                className="flex items-center gap-2 rounded-md hover:bg-[var(--hover-muted)]/30 cursor-pointer"
                onClick={() => setIsSheetOpen(true)}
              >
                <Maximize2 className="h-4 w-4 text-[var(--muted-foreground)]" />
                <span className="text-sm">Expand to view all ({allCount})</span>
              </DropdownMenuItem>
            </div>
          ) : null}
        </DropdownMenuContent>
      </DropdownMenu>
      <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <SheetContent side="right" className="w-[50vw] max-w-[50vw] min-h-0">
          <SheetHeader>
            <SheetTitle>All priorities ({allCount})</SheetTitle>
          </SheetHeader>
          <ScrollArea className="flex-1 min-h-0 px-2 h-[calc(100vh-100px)]">
            <div className="divide-y divide-[var(--border)]">
              {computedItems.map((item) => {
                const status = String(item.status || "idea").toLowerCase();
                const statusBadge = (() => {
                  if (status === "action") {
                    return (
                      <Badge
                        variant="secondary"
                        className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-1.5 border border-blue-200 dark:border-blue-700 h-6"
                      >
                        <Zap className="h-3 w-3" />
                      </Badge>
                    );
                  }
                  if (status === "confirmed") {
                    return (
                      <Badge
                        variant="secondary"
                        className="bg-green-100 dark:bg-green-900 text-green-900 dark:text-green-200 text-xs px-1.5 border border-green-700 dark:border-green-600 h-6"
                      >
                        <Check className="h-4 w-4" />
                      </Badge>
                    );
                  }
                  return (
                    <Badge
                      variant="secondary"
                      className="bg-yellow-50 dark:bg-yellow-500 text-black dark:text-white text-xs px-1.5 py-0.5 border border-yellow-300 dark:border-yellow-700 h-6"
                    >
                      <Lightbulb className="h-3 w-3" />
                    </Badge>
                  );
                })();

                return (
                  <div
                    key={item.id}
                    className="flex items-start gap-2 p-3 transition-colors hover:bg-[var(--siift-bold-accent)]/10 dark:hover:bg-[var(--siift-bold-accent)]/20 cursor-pointer"
                    onClick={() => {
                      handleEntryClick(item);
                      setIsSheetOpen(false);
                    }}
                  >
                    {statusBadge}
                    <div className="flex flex-col flex-1">
                      <span className="text-sm text-foreground font-medium">
                        {toText(item.title)}
                      </span>
                      <span className="text-[11px] text-muted-foreground">
                        {item.topic}
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          </ScrollArea>
        </SheetContent>
      </Sheet>
    </>
  );
});
