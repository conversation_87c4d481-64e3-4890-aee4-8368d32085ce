"use client";

import { useSidebar } from "@/components/ui/sidebar";
import { SidebarButton } from "@/components/ui/sidebar-button";
import { useAnalytics } from "@/hooks/useAnalytics";
import { ICON_SIZES } from "@/lib/constants";
import { FileText, Home } from "lucide-react";
import { useRouter } from "next/navigation";
import { PrioritiesDropdown } from "./PrioritiesDropdown";
import type {
  ProjectTopicSummary,
  TopicEntry,
} from "@/hooks/queries/useTopics";
import { ProjectDetailHeader } from "./ProjectDetailHeader";
import { useUserProfile } from "@/hooks/queries/useUser";
import { CreditsButton } from "@/components/ui/CreditsButton";
import { useFocusQueueStore } from "@/stores/focusQueueStore";
import { Target } from "lucide-react";

interface ProjectHeaderProps {
  activeContent: string | null;
  setActiveContent: (content: string | null) => void;
  selectedBusinessItem: any;
  onBackToItems: () => void;
  projectTitle: string;
  projectName: string;
  allTopics?: any[];
  entriesByTopic?: Record<string, any[]>;
  isLoading?: boolean;
}

export function ProjectHeader({
  activeContent,
  setActiveContent,
  selectedBusinessItem,
  onBackToItems,
  projectTitle,
  projectName,
  allTopics,
  entriesByTopic,
  isLoading = false,
}: ProjectHeaderProps) {
  const router = useRouter();
  const enqueueFocus = useFocusQueueStore((s) => s.enqueue);
  const { trackClick, trackCustomEvent } = useAnalytics();
  const { isMobile } = useSidebar();
  const { data: profile } = useUserProfile();
  const credits =
    (profile as any)?.credits ??
    (profile as any)?.creditBalance ??
    (profile as any)?.plan?.credits ??
    0;

  // If an item is selected, show the detail header
  if (selectedBusinessItem) {
    console.log(
      "[ProjectHeader] showing detail header for item",
      selectedBusinessItem?.id
    );
    return (
      <ProjectDetailHeader
        selectedBusinessItem={selectedBusinessItem}
        onBackToItems={onBackToItems}
        projectName={projectName}
      />
    );
  }
  return (
    <header className="flex flex-col h-20 shrink-0 transition-[width] ease-linear border-b  border-[var(--siift-light-mid)]/50 ">
      <div
        className={`flex items-center w-full flex-1 px-4 ${
          isMobile ? "justify-between" : "gap-4"
        }`}
      >
        {/* Mobile: Home button */}
        {isMobile && (
          <SidebarButton
            onClick={() => {
              trackClick("home-button", "project-header");
              trackCustomEvent("navigation_clicked", {
                destination: "dashboard",
                from_page: "project-detail",
                location: "header",
              });
              router.push("/user-dashboard");
            }}
            icon={Home}
            variant="ghost"
            size="lg"
            layout="icon-only"
            showBorder={true}
            hoverColor="grey"
            hoverScale={true}
            iconClassName={ICON_SIZES.lg}
          />
        )}

        {/* Left side - Priorities dropdown */}
        <div
          className={`flex items-center gap-2 ${isMobile ? "hidden" : "block"}`}
        >
          <PrioritiesDropdown
            allTopics={allTopics}
            entriesByTopic={entriesByTopic}
            isLoading={isLoading}
          />
        </div>

        {/* Center - Mobile priorities dropdown */}
        <div
          className={`ml-2 justify-center ${isMobile ? "flex-1" : "flex-1"}`}
        >
          <div className={`${isMobile ? "block" : "hidden"}`}>
            <div className="flex items-center gap-2">
              <PrioritiesDropdown
                allTopics={allTopics}
                entriesByTopic={entriesByTopic}
                isLoading={isLoading}
              />
            </div>
          </div>
        </div>

        {/* Right side - Drafts and Profile */}
        <div className="flex items-center gap-2 h-full">
          {process.env.NODE_ENV === "development" && (
            <SidebarButton
              onClick={() => {
                enqueueFocus("problem", 3000, 4);
              }}
              icon={Target}
              variant="ghost"
            />
          )}

          {/* Credits button */}
          <CreditsButton
            variant="outline"
            size="md"
            layout="horizontal"
            showBorder={true}
            trackFrom="project-header"
            navigateToBilling={false}
          />

          {/* Drafts button */}
          <SidebarButton
            onClick={() => {
              trackClick("drafts-button", "project-header");
              trackCustomEvent("content_section_opened", {
                section: "drafts",
                location: "project-header",
              });
              setActiveContent("drafts");
            }}
            icon={FileText}
            variant="ghost"
            size="lg"
            layout="icon-only"
            showBorder={true}
            hoverColor="grey"
            hoverScale={true}
            iconClassName={ICON_SIZES.lg}
          />

          {/* User profile button removed as requested */}
        </div>
      </div>
    </header>
  );
}
