"use client";

import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { StatusCountBadge } from "@/components/ui/status-count-badge";
import { useProjects } from "@/hooks/queries/useProjects";
import {
  useAllProjectTopics,
  useAllTopicEntries,
} from "@/hooks/queries/useTopics";
import { computeEntryCounts } from "@/utils/entryCounts";
import { Clock } from "lucide-react";
import { useRouter } from "next/navigation";
import { formatRelativeTime } from "../../lib/utils";
import { Logo } from "../ui/logo";
import { Tooltip, TooltipTrigger } from "../ui/tooltip";

// Updated: Removed add new project button, removed progress, reduced padding, added hover effects

export function DashboardProjectsSection() {
  const router = useRouter();
  const { data, isLoading, error } = useProjects();
  const projects = Array.isArray(data) ? data : [];

  // Sort projects by updatedAt (most recent first)
  const sortedProjects = [...projects].sort((a, b) => {
    const dateA = new Date(a.updatedAt || a.createdAt);
    const dateB = new Date(b.updatedAt || b.createdAt);
    return dateB.getTime() - dateA.getTime();
  });

  return (
    <div className="space-y-6">
      <div className="max-w-6xl mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-1 gap-8">
          {/* Left side - Recent Projects (1 per row) */}
          <div className="lg:col-span-1">
            <Card className="bg-gray-50 dark:bg-card">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      Projects
                    </CardTitle>
                    <CardDescription className="text-xs mt-1">
                      Your active projects (sorted by last updated)
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {isLoading && (
                  <div className="text-sm text-muted-foreground">
                    Loading projects...
                  </div>
                )}
                {error && (
                  <div className="text-sm text-destructive">
                    Failed to load projects
                  </div>
                )}
                <div className="grid gap-3 grid-cols-1 lg:grid-cols-2">
                  {/* Project Cards */}
                  {sortedProjects.map((project: any) => {
                    const lastUpdated = project.updatedAt || project.createdAt;
                    const isRecentlyUpdated =
                      project.updatedAt &&
                      new Date(project.updatedAt).getTime() !==
                        new Date(project.createdAt).getTime();

                    return (
                      <Card
                        key={project.id}
                        className="bg-gray-200/50 dark:bg-secondary/90 hover:shadow-md hover:bg-[var(--accent)]/10 dark:hover:bg-accent/50 transition-all duration-200 cursor-pointer group py-1 hover:border-1 hover:border-[var(--accent)]"
                        onClick={() => router.push(`/projects/${project.id}`)}
                      >
                        <CardContent className="px-4 py-2">
                          <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between">
                                <h3 className="font-medium truncate">
                                  {project.name}
                                </h3>
                                <div className="flex items-center gap-2 ml-2">
                                  <ProjectProgress projectId={project.id} />
                                </div>
                              </div>

                              {lastUpdated && (
                                <div className="flex justify-between gap-2">
                                  <div className="flex items-center gap-1 mt-1 text-xs text-muted-foreground">
                                    <Clock className="h-3 w-3" />
                                    <span>
                                      {isRecentlyUpdated
                                        ? "Updated"
                                        : "Created"}{" "}
                                      {formatRelativeTime(lastUpdated)}
                                    </span>
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <ProjectCounts projectId={project.id} />
                                  </div>
                                </div>
                              )}
                              {/* Project counts */}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

function ProjectCounts({ projectId }: { projectId: string }) {
  const { data: topics } = useAllProjectTopics(projectId);
  const { data: entriesByTopic } = useAllTopicEntries(
    projectId,
    Array.isArray(topics)
      ? topics.map((t) => ({
          sectionId: t.mappedCategoryId ?? t.sectionId,
          topicId: t.topicId,
          numericTopicId: t.numericTopicId,
          title: t.title,
          mappedLabel: t.mappedLabel,
        }))
      : [],
    "initial"
  );

  // Merge entries across all topics (prefer store not available here; merge backend only)
  const allEntries = (() => {
    const list: any[] = [];
    if (!entriesByTopic) return list;
    for (const key of Object.keys(entriesByTopic)) {
      const arr = entriesByTopic[key] || [];
      for (const e of arr) list.push(e);
    }
    return list;
  })();

  const {
    ideas,
    actions,
    confirmed: results,
  } = computeEntryCounts(allEntries as any[]);

  return (
    <div className="mt-2 space-y-1">
      <div className="flex items-center gap-2 text-[10px]">
        <StatusCountBadge type="idea" count={ideas} showTooltip />
        <StatusCountBadge type="action" count={actions} showTooltip />
        <StatusCountBadge type="confirmed" count={results} showTooltip />
      </div>
    </div>
  );
}

function ProjectProgress({ projectId }: { projectId: string }) {
  const { data: topics } = useAllProjectTopics(projectId);
  const { data: entriesByTopic } = useAllTopicEntries(
    projectId,
    Array.isArray(topics)
      ? topics.map((t) => ({
          sectionId: t.mappedCategoryId ?? t.sectionId,
          topicId: t.topicId,
          numericTopicId: t.numericTopicId,
          title: t.title,
          mappedLabel: t.mappedLabel,
        }))
      : [],
    "initial"
  );

  if (!topics || !entriesByTopic) {
    return <Logo size={24} animated={true} />;
  }

  const totalTopics = Array.isArray(topics) ? topics.length : 0;
  const completedTopics = totalTopics
    ? topics.reduce((count, t) => {
        const entries = entriesByTopic?.[t.topicId] || [];
        const isConfirmed =
          Array.isArray(entries) &&
          entries.some((e: any) => e.status === "confirmed");
        return count + (isConfirmed ? 1 : 0);
      }, 0)
    : 0;
  const progress = totalTopics
    ? Math.round((completedTopics / totalTopics) * 100)
    : 0;

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <p className="text-xs text-muted-foreground">{progress}%</p>
      </TooltipTrigger>
    </Tooltip>
  );
}
