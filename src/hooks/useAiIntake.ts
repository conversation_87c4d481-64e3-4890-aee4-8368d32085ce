"use client";

import { useCallback, useEffect, useRef } from "react";
import { useAiIntakeStore } from "@/stores/aiIntakeStore";
import { useChatStore } from "@/stores/chatStore";
import { safeLocalStorage } from "@/lib/storage";
import { useQueryClient } from "@tanstack/react-query";
import { sseManager } from "@/lib/sseManager";
import { useFocusQueueStore } from "@/stores/focusQueueStore";
import { queryKeys } from "@/lib/queryClient";

export function useAiIntake() {
  const {
    sessionId,
    stage,
    progress,
    insertedCount,
    error,
    setSessionId,
    setStage,
    appendProgress,
    setInsertedCount,
    setError,
    setSiiftReady,
    reset,
  } = useAiIntakeStore();

  const unsubscribeRef = useRef<null | (() => void)>(null);
  const currentProjectIdRef = useRef<string | null>(null);
  const queryClient = useQueryClient();
  const refreshTimerRef = useRef<NodeJS.Timeout | null>(null);
  const statusFlagsRef = useRef<{
    postedGenerating: boolean;
    postedFetching: boolean;
  }>({ postedGenerating: false, postedFetching: false });
  const onboardingTimerRef = useRef<NodeJS.Timeout | null>(null);
  const onboardingStepRef = useRef<number>(0);
  const onboardingFocusQueuedRef = useRef<boolean>(false);
  const postedMessagesRef = useRef<Set<string>>(new Set());

  const ONBOARDING_MESSAGES = [
    "Welcome to your project workspace!",
    'The "siift-table" on the right is where all your business information will be organized and acted upon, step by step.',
    "As you ideate, iterate and validate topics, more will unlock and your progress bar will increase.",
    "Our bots are currently researching your idea on the web, to get a big picture view of the opportunity - this may take a minute!",
    "Once the first round of research is done, you'll see the first few topics fill in",
  ];

  const scheduleProjectDataRefresh = useCallback(
    (immediate?: boolean, specificTopics?: string[]) => {
      const projectId = currentProjectIdRef.current;
      if (!projectId) return;

      const doRefresh = async () => {
        console.log("[useAiIntake] Selective refresh strategy:", {
          immediate,
          specificTopics,
          projectId,
        });

        // Instead of mass invalidation, be selective based on what AI events occurred
        const keysToInvalidate: any[] = [];

        // Always invalidate topics list when new topics are generated
        keysToInvalidate.push(["all-project-topics-v2", projectId]);

        // Only invalidate topic entries if specific topics were updated, or if topics array provided
        if (specificTopics && specificTopics.length > 0) {
          // Invalidate entries for specific topics only
          specificTopics.forEach((topicId) => {
            keysToInvalidate.push(["topic-entries", projectId, topicId]);
          });
          // Also invalidate the aggregate entries query to refresh display
          keysToInvalidate.push(["all-topic-entries", projectId]);
        } else {
          // Fallback: invalidate all entries (only when we don't know specific topics)
          keysToInvalidate.push(["all-topic-entries", projectId]);
        }

        // Business sections change less frequently, only invalidate when structure changes
        // (we'll add specific triggers for this later)
        keysToInvalidate.push(["business-sections", projectId]);

        // Always refresh credits alongside project updates
        keysToInvalidate.push(queryKeys.credits());

        // Invalidate (mark stale) but don't force immediate refetch
        // Let the components decide when to refetch based on their store-first logic
        keysToInvalidate.forEach((key) => {
          console.log("[useAiIntake] Invalidating query key:", key);
          // Credits key is already in the correct format, others are array literals
          queryClient.invalidateQueries({ queryKey: key as any });
        });

        // If this refresh is marked immediate (e.g., topic complete), force active queries to refetch now
        if (immediate) {
          for (const key of keysToInvalidate) {
            try {
              await queryClient.refetchQueries({
                queryKey: key as any,
                type: "active",
              });
              console.log("[useAiIntake] Refetched query key:", key);
            } catch (err) {
              console.warn("[useAiIntake] Refetch failed for key:", key, err);
            }
          }
        } else {
          // Don't force refetch - let cache expiration and store-first logic handle it
          console.log(
            "[useAiIntake] Selective invalidation complete, letting store-first logic decide on refetch"
          );
        }
      };

      if (immediate) {
        if (refreshTimerRef.current) clearTimeout(refreshTimerRef.current);

        refreshTimerRef.current = null;

        void doRefresh();

        return;
      }

      if (refreshTimerRef.current) clearTimeout(refreshTimerRef.current);
      refreshTimerRef.current = setTimeout(() => {
        refreshTimerRef.current = null;
        void doRefresh();
      }, 750);
    },
    [queryClient]
  );

  // Chat store hooks for live feedback in chat UI
  const {
    addMessage,
    updateLastMessage,
    appendToLastMessage,
    setProjectLoading,
    setProjectStreaming,
    addCtaMessage,
  } = useChatStore();
  const enqueueFocus = useFocusQueueStore((s) => s.enqueue);

  const ensureAiMessage = useCallback(() => {
    const projectId = currentProjectIdRef.current;
    if (!projectId) return;
    try {
      const { messages } = useChatStore.getState();
      const last =
        messages.length > 0 ? messages[messages.length - 1] : undefined;
      if (!last || last.isCurrentUser) {
        updateLastMessage("");
      }
    } catch {}
  }, [updateLastMessage]);

  const openStream = useCallback(
    (sessId: string, projectId?: string) => {
      console.log("[useAiIntake] openStream", { sessId, projectId });
      if (projectId) currentProjectIdRef.current = projectId;

      if (unsubscribeRef.current) {
        try {
          unsubscribeRef.current();
        } catch {}
        unsubscribeRef.current = null;
      }

      // High-level chat updates only (no raw logs)
      const add = (msg: string) => appendProgress(msg);

      const postChatStatus = (text: string) => {
        try {
          const projectId = currentProjectIdRef.current;
          if (!projectId) return;

          // Store onboarding message at project level (not scope-specific)
          const onboardingKey = `onboarding_messages_${projectId}`;
          const existingMessages = safeLocalStorage.getJSON<any[]>(
            onboardingKey,
            []
          );

          // Dedupe: avoid posting the same message more than once
          const normalizedText = String(text || "").trim();
          if (!normalizedText) return;
          const alreadyInStorage = existingMessages?.some(
            (m: any) => String(m?.message || "").trim() === normalizedText
          );
          const alreadyInSession =
            postedMessagesRef.current.has(normalizedText);
          if (alreadyInStorage || alreadyInSession) {
            return;
          }
          postedMessagesRef.current.add(normalizedText);

          const message = {
            id: `ai-onboarding-${Date.now()}`,
            user: "siift AI",
            avatar: "",
            message: normalizedText,
            timestamp: new Date().toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
            }),
            isCurrentUser: false,
            isOnboarding: true, // Mark as onboarding message
          } as any;

          // Persist onboarding message for this project
          const updatedMessages = [...existingMessages, message];
          safeLocalStorage.setJSON(onboardingKey, updatedMessages);

          // Add to current chat
          const { addMessage: _addMessage } = useChatStore.getState();
          _addMessage?.(message);
          if (text.includes("on the right is where")) {
            try {
              enqueueFocus?.("business-sections", 2000, 5);
            } catch {}
          }
          if (text.includes("iterate and validate topics")) {
            try {
              enqueueFocus?.("project-progress", 2000, 3);
            } catch {}
          }
        } catch {}
      };

      const updateChatStatus = (text: string) => {
        try {
          const { updateLastMessage: _updateLastMessage } =
            useChatStore.getState();
          _updateLastMessage?.(text);
        } catch {}
      };

      const startOnboardingSequence = () => {
        if (onboardingTimerRef.current)
          clearInterval(onboardingTimerRef.current);
        onboardingStepRef.current = 0;

        // Post first message immediately
        postChatStatus(ONBOARDING_MESSAGES[0]);
        onboardingStepRef.current = 1;

        // Queue a single initial focus item on first onboarding message
        if (!onboardingFocusQueuedRef.current) {
          onboardingFocusQueuedRef.current = true;
        }

        // Then post NEW bubbles every 10 seconds with subsequent messages
        onboardingTimerRef.current = setInterval(() => {
          if (onboardingStepRef.current < ONBOARDING_MESSAGES.length) {
            // Use postChatStatus instead of updateChatStatus to create NEW bubbles
            postChatStatus(ONBOARDING_MESSAGES[onboardingStepRef.current]);
            onboardingStepRef.current++;
          }
          // Keep the last message visible indefinitely
        }, 10000);
      };

      const stopOnboardingSequence = () => {
        if (onboardingTimerRef.current) {
          clearInterval(onboardingTimerRef.current);
          onboardingTimerRef.current = null;
        }
      };

      const handleTokenStream = (ev: MessageEvent) => {
        try {
          const data = JSON.parse(ev.data as string);

          if (data.tokens && Array.isArray(data.tokens)) {
            const agentName = String(
              data.agent || data.agent_name || ""
            ).toLowerCase();
            // Ignore tool/suggestion/siift generator token streams for chat UI streaming state
            if (
              agentName.includes("tools") ||
              agentName.includes("siift_generator") ||
              agentName.includes("suggest")
            ) {
              return;
            }
            // Summarize stream activity without dumping raw chunks
            const joined = String(data.tokens.join(""));
            if (joined.trim()) add("Generating content…");
            const projectId = currentProjectIdRef.current;
            if (projectId) setProjectStreaming(projectId, true);
          }
        } catch {}
      };

      const handleToken = (ev: MessageEvent) => {
        try {
          const data = JSON.parse(ev.data as string);
          // Only high-level notes
          if (String(data.agent || data.agent_name).includes("siift")) {
            add("Analyzing topics…");
          }

          const projectId = currentProjectIdRef.current;
          const agentName = String(
            data.agent || data.agent_name || ""
          ).toLowerCase();
          // Ignore tool/suggestion/siift generator tokens for chat UI streaming state
          if (
            agentName.includes("tools") ||
            agentName.includes("siift_generator") ||
            agentName.includes("suggest")
          ) {
            return;
          }
          if (projectId) setProjectStreaming(projectId, true);
        } catch {}
      };

      const handleConnected = (ev: MessageEvent) => {
        try {
          const data = JSON.parse(ev.data as string);
          add(`connected: ${data.status || "connected"}`);
        } catch {}
      };

      const handleContextUpdate = (ev: MessageEvent) => {
        try {
          const data = JSON.parse(ev.data as string);
          add(`context: ${data.field} = ${data.value}`);

          // Trigger targeted refresh based on context field
          const field = data.field;
          const value = data.value;

          console.log("[useAiIntake] Context update:", { field, value });

          // Specific refetch triggers based on context type
          if (field === "generated_siift_entries" || field === "siift_data") {
            // New entries generated - trigger immediate refresh
            scheduleProjectDataRefresh(true);
          } else if (field === "conversation_stage" && value === "complete") {
            // Stage transition - trigger immediate refresh
            scheduleProjectDataRefresh(true);
          } else if (field === "conversation_stage") {
            // Other stage changes - debounced refresh
            scheduleProjectDataRefresh();
          }
        } catch {
          // Fallback to debounced refresh
          scheduleProjectDataRefresh();
        }
      };

      const handleChatTurnError = (ev: MessageEvent) => {
        try {
          const data = JSON.parse(ev.data as string);
          const msg = data?.error_message || data?.error || "Chat turn failed";
          add(`chat turn error: ${msg}`);
          setError(String(msg));
        } catch {
          setError("Chat turn failed");
        }
        const projectId = currentProjectIdRef.current;
        if (projectId) setProjectStreaming(projectId, false);
      };

      const handleIntakeComplete = (ev: MessageEvent) => {
        try {
          const data = JSON.parse(ev.data as string);
          add(
            `intake complete: fields=${(data?.extracted_fields || []).length}`
          );
        } catch {}
        // Move to ingestion similar to stream_complete flow
        setStage("ingesting");
        scheduleProjectDataRefresh(true);
      };

      const handleIntakeError = (ev: MessageEvent) => {
        try {
          const data = JSON.parse(ev.data as string);
          const msg = data?.error_message || "Intake failed";
          add(`intake error: ${msg}`);
          setError(String(msg));
        } catch {
          setError("Intake failed");
        }
        const projectId = currentProjectIdRef.current;
        if (projectId) {
          setProjectStreaming(projectId, false);
          setProjectLoading(projectId, false);
        }
        setStage("ready");
      };

      const handleCoachingError = (ev: MessageEvent) => {
        try {
          const data = JSON.parse(ev.data as string);
          const msg = data?.error_message || "Coaching failed";
          add(`coaching error: ${msg}`);
          setError(String(msg));
        } catch {
          setError("Coaching failed");
        }
        const projectId = currentProjectIdRef.current;
        if (projectId) setProjectStreaming(projectId, false);
      };

      const handleSuggestionsError = (ev: MessageEvent) => {
        try {
          const data = JSON.parse(ev.data as string);
          add(`suggestions error for topic ${data?.topic_id}: ${data?.error}`);
        } catch {
          add("suggestions error");
        }
        scheduleProjectDataRefresh();
      };

      const handleParallelGenerationComplete = (ev: MessageEvent) => {
        try {
          const data = JSON.parse(ev.data as string);
          add(
            `parallel generation complete: subjects=${data?.subjects_processed}`
          );
        } catch {}
        const projectId = currentProjectIdRef.current;
        if (projectId) setProjectLoading(projectId, false);
        scheduleProjectDataRefresh(true);
      };

      const handleParallelGenerationError = (ev: MessageEvent) => {
        try {
          const data = JSON.parse(ev.data as string);
          const msg = data?.error_message || "Parallel generation error";
          add(`parallel generation error: ${msg}`);
          setError(String(msg));
        } catch {
          setError("Parallel generation error");
        }
        const projectId = currentProjectIdRef.current;
        if (projectId) setProjectLoading(projectId, false);
        scheduleProjectDataRefresh();
      };

      const handleTopicGenerationError = (ev: MessageEvent) => {
        try {
          const data = JSON.parse(ev.data as string);
          add(
            `topic generation error: ${data?.topic_layer} - ${data?.error_message}`
          );
        } catch {
          add("topic generation error");
        }
        scheduleProjectDataRefresh();
      };

      const handleToolCall = (ev: MessageEvent) => {
        try {
          const data = JSON.parse(ev.data as string);
          add(`tool call: ${data?.tool || "unknown"}`);
        } catch {
          add("tool call");
        }
      };

      const handleSuggestionsStarted = (ev: MessageEvent) => {
        try {
          const data = JSON.parse(ev.data as string);
          const { topic_id, entry_ids, count } = data;
          console.log(
            `[useAiIntake] Suggestions started for topic ${topic_id}: ${count} entries (${
              entry_ids?.length || 0
            } IDs)`
          );
          add(`Generating suggestions for topic ${topic_id}...`);
        } catch (error) {
          console.error(
            "[useAiIntake] Error parsing suggestions_started event:",
            error
          );
        }
      };

      const handleSuggestionsProgress = (ev: MessageEvent) => {
        try {
          const data = JSON.parse(ev.data as string);
          const { topic_id, entry_id, suggested_actions } = data;
          console.log(
            `[useAiIntake] Suggestions progress for entry ${entry_id} in topic ${topic_id}:`,
            suggested_actions?.length || 0,
            "actions"
          );

          // Dispatch custom event for SuggestionsCell to listen to
          if (typeof window !== "undefined") {
            window.dispatchEvent(
              new CustomEvent("siift:suggestions-progress", {
                detail: { topic_id, entry_id, suggested_actions },
              })
            );
          }
        } catch (error) {
          console.error(
            "[useAiIntake] Error parsing suggestions_progress event:",
            error
          );
        }
      };

      const handleSuggestionsCompleted = (ev: MessageEvent) => {
        try {
          const data = JSON.parse(ev.data as string);
          const { topic_id, entries_updated } = data;
          console.log(
            `[useAiIntake] Suggestions completed for topic ${topic_id}: ${entries_updated} entries updated`
          );
          add(
            `Suggestions generated for topic ${topic_id} (${entries_updated} entries)`
          );

          // Trigger refetch for the specific topic
          scheduleProjectDataRefresh(true, [String(topic_id)]);

          // Dispatch custom event for SuggestionsCell components to refetch
          if (typeof window !== "undefined") {
            window.dispatchEvent(
              new CustomEvent("siift:suggestions-completed", {
                detail: { topic_id, entries_updated },
              })
            );
          }
          // Ensure chat UI is not stuck in streaming due to suggestion flows
          const projectId = currentProjectIdRef.current;
          if (projectId) setProjectStreaming(projectId, false);
        } catch (error) {
          console.error(
            "[useAiIntake] Error parsing suggestions_completed event:",
            error
          );
          scheduleProjectDataRefresh(true);
          const projectId = currentProjectIdRef.current;
          if (projectId) setProjectStreaming(projectId, false);
        }
      };

      const handleSiiftProgress = (ev: MessageEvent) => {
        try {
          const data = JSON.parse(ev.data as string);
          add(String(data.progress ?? ""));
        } catch {}
      };

      const handleSubjectStart = (ev: MessageEvent) => {
        try {
          const data = JSON.parse(ev.data as string);
          add(`subject start: ${data.subject}`);
        } catch {}
        if (!statusFlagsRef.current.postedGenerating) {
          startOnboardingSequence();
          statusFlagsRef.current.postedGenerating = true;
        }
      };

      const handleSubjectComplete = (ev: MessageEvent) => {
        try {
          const data = JSON.parse(ev.data as string);
          add(
            `subject complete: ${data.subject} (${
              data.entries_generated ?? "?"
            } entries)`
          );

          // Otherwise, delay onboarding seeding by 1 second to avoid flicker
          window.setTimeout(() => {
            enqueueFocus?.(data.subject.toLowerCase(), 2000, 5);
          }, 1000);

          // Trigger immediate targeted refetch for the specific subject/topic
          const topicId = data.topic_id || data.topicId || data.subject;
          if (topicId) {
            console.log(
              "[useAiIntake] Subject complete - triggering refetch for topic:",
              topicId
            );
            scheduleProjectDataRefresh(true, [String(topicId)]);
          } else {
            scheduleProjectDataRefresh(true);
          }
        } catch {
          // Fallback to general refresh
          scheduleProjectDataRefresh(true);
        }
      };

      const handleTopicStart = (ev: MessageEvent) => {
        try {
          const data = JSON.parse(ev.data as string);
          add(`topic start: ${data.topic_layer}`);
        } catch {}
      };

      const handleTopicComplete = (ev: MessageEvent) => {
        try {
          const data = JSON.parse(ev.data as string);
          add(
            `topic complete: ${data.topic_layer} (${
              data.entries_generated ?? "?"
            } entries)`
          );
          // here is the way we show a effect on topic layer
          enqueueFocus?.(data.topic_layer.toLowerCase(), 1000, 4);

          // Trigger immediate targeted refetch for the specific topic
          const topicId = data.topic_id || data.topicId || data.topic_layer;
          if (topicId) {
            console.log(
              "[useAiIntake] Topic complete - triggering refetch for topic:",
              topicId
            );
            scheduleProjectDataRefresh(true, [String(topicId)]);
          } else {
            scheduleProjectDataRefresh(true);
          }
        } catch {
          // Fallback to general refresh
          scheduleProjectDataRefresh(true);
        }
      };

      const handleComplete = () => {
        // Primary completion signal: stream_complete
        setStage("ingesting");

        add("Stream complete, starting ingestion...");
        const projectId = currentProjectIdRef.current;
        if (projectId) {
          setProjectStreaming(projectId, false);
          setProjectLoading(projectId, false);
        }
        stopOnboardingSequence();
        scheduleProjectDataRefresh(true);
      };

      const handleIngest = (ev: MessageEvent) => {
        try {
          const data = JSON.parse(ev.data as string);
          setInsertedCount(Number(data.inserted ?? 0));
          add(`ingested ${data.inserted ?? 0} entries...`);
          ensureAiMessage();
          appendToLastMessage?.(`\nIngested ${data.inserted ?? 0} entries...`);
        } catch {}
      };

      const handleIngestComplete = (ev: MessageEvent) => {
        try {
          const data = JSON.parse(ev.data as string);
          setInsertedCount(Number(data.inserted ?? 0));
          add(
            `ingestion complete - ${data.inserted ?? 0} entries, ${
              data.topics_covered ?? 0
            } topics`
          );
        } catch {}
        setStage("ready");
        setSiiftReady(true);
        const projectId = currentProjectIdRef.current;
        if (projectId) {
          setProjectStreaming(projectId, false);
          stopOnboardingSequence();
          updateChatStatus(
            "Your siift table is ready! Start exploring the topics on the right."
          );
          scheduleProjectDataRefresh(true);

          try {
            if (typeof addCtaMessage === "function") {
              addCtaMessage(
                "Click Start siifting to begin working with your topics.",
                { type: "refetch_topics", label: "Start siifting" }
              );
            }
          } catch {}
        }
      };

      const handleIngestError = (ev: MessageEvent) => {
        console.log("[useAiIntake] siift_ingest_error");
        try {
          const data = JSON.parse(ev.data as string);
          setError(`Ingestion failed: ${data.error || "Unknown error"}`);
        } catch {
          setError("Ingestion failed");
        }
        setStage("ready");
      };

      const handleSseError = (ev: MessageEvent) => {
        console.log("[useAiIntake] event-source error");
        try {
          const data = JSON.parse(ev.data as string);
          const msg = data?.error || data?.message || "Stream connection error";
          setError(String(msg));
          add(`error: ${msg}`);
        } catch {
          if (stage !== "ready") setError("Stream connection error");
        }
        const projectId = currentProjectIdRef.current;
        if (projectId) {
          setProjectStreaming(projectId, false);
          setProjectLoading(projectId, false);
        }
      };

      unsubscribeRef.current = sseManager.subscribe(sessId, {
        connected: handleConnected,
        token_stream: handleTokenStream,
        token: handleToken,
        context_update: handleContextUpdate,
        siift_progress: handleSiiftProgress,
        parallel_generation_start: (_e) => {
          const p = currentProjectIdRef.current;
          if (p) setProjectLoading(p, true);
        },
        parallel_generation_complete: handleParallelGenerationComplete,
        parallel_generation_error: handleParallelGenerationError,
        subject_generation_start: handleSubjectStart,
        subject_generation_complete: handleSubjectComplete,
        topic_generation_start: handleTopicStart,
        topic_generation_complete: handleTopicComplete,
        topic_generation_error: handleTopicGenerationError,
        siift_generation_complete: (_e) => {
          scheduleProjectDataRefresh(true);
        },
        // Suggestion events
        suggestions_started: handleSuggestionsStarted,
        suggestions_progress: handleSuggestionsProgress,
        suggestions_completed: handleSuggestionsCompleted,
        suggestions_error: handleSuggestionsError,
        tool_call: handleToolCall,
        complete: (_e) => handleComplete(),
        stream_complete: (_e) => handleComplete(),
        // Newer events emitted by backend during coaching/intake
        chat_turn_complete: (_e) => handleComplete(),
        chat_turn_error: handleChatTurnError,
        intake_complete: handleIntakeComplete,
        intake_error: handleIntakeError,
        coaching_complete: (_e) => handleComplete(),
        coaching_error: handleCoachingError,
        siift_ingest: handleIngest,
        siift_ingest_complete: handleIngestComplete,
        siift_ingest_error: handleIngestError,
        keepalive: (_e) => {},
        error: (e) => handleSseError(e),
      });
    },
    [
      appendProgress,
      setError,
      setInsertedCount,
      setSiiftReady,
      setStage,
      stage,
      ensureAiMessage,
      appendToLastMessage,
      setProjectStreaming,
      queryClient,
      addCtaMessage,
      enqueueFocus,
    ]
  );

  const cleanup = useCallback(() => {
    if (onboardingTimerRef.current) {
      clearInterval(onboardingTimerRef.current);
      onboardingTimerRef.current = null;
    }
    try {
      unsubscribeRef.current?.();
    } catch {}
    unsubscribeRef.current = null;
  }, []);

  useEffect(() => () => cleanup(), [cleanup]);

  const startIntake = useCallback(
    async (projectId: string, message: string) => {
      console.log("[useAiIntake] startIntake", { projectId, message });
      setStage("streaming_intake");
      // reset except keep session if existing
      reset();
      setStage("streaming_intake");

      try {
        let sessId = null;

        // 1) First try to find existing active session for this project
        try {
          const existingResp = await fetch(
            `/api/ai-chat/projects/${projectId}/sessions`,
            {
              credentials: "include",
            }
          );

          if (existingResp.ok) {
            const sessions = await existingResp.json();
            console.log("[useAiIntake] Existing sessions:", sessions);

            // Find the latest active session
            const activeSessions: Array<Record<string, unknown>> =
              Array.isArray(sessions)
                ? sessions
                : Array.isArray((sessions as any)?.data)
                ? (sessions as any).data
                : [];
            const activeSession = activeSessions.find(
              (s) => (s as any)?.status === "active"
            ) as any;

            if (activeSession) {
              sessId =
                activeSession.id ||
                activeSession.session_id ||
                activeSession.sessionId;
              console.log("[useAiIntake] Found existing session:", sessId);
            }
          }
        } catch (error) {
          console.log(
            "[useAiIntake] No existing sessions found, will create new one"
          );
        }

        // 2) If no existing session, create a new one
        if (!sessId) {
          const resp = await fetch("/api/ai-chat/sessions", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ projectId }),
            credentials: "include",
          });

          if (!resp.ok) {
            const errorText = await resp.text();
            console.error(
              "[useAiIntake] Session creation failed:",
              resp.status,
              resp.statusText,
              errorText
            );
            setError(
              `Failed to create AI session: ${resp.status} ${resp.statusText}`
            );
            setStage("ready");
            return;
          }

          const json = await resp.json();
          console.log("[useAiIntake] Session response:", json);
          sessId =
            json?.session_id ?? json?.id ?? json?.data?.id ?? json?.sessionId;

          if (!sessId) {
            console.error("[useAiIntake] No session ID in response:", json);
            setError("Failed to get session ID from response");
            setStage("ready");
            return;
          }

          console.log("[useAiIntake] Created new session:", sessId);
        }

        setSessionId(sessId);
        try {
          safeLocalStorage.setItem(`intake_session_${projectId}`, sessId);
        } catch {}

        // 3) open SSE
        openStream(sessId, projectId);

        // 4) kick off intake using the proper chat endpoint with new stage API
        const chatResponse = await fetch(
          `/api/ai-chat/sessions/${sessId}/chat`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              content: message,
              stage: "intake",
            }),
            credentials: "include",
          }
        );

        if (!chatResponse.ok) {
          const errorText = await chatResponse.text();
          console.error(
            "[useAiIntake] Chat failed:",
            chatResponse.status,
            chatResponse.statusText,
            errorText
          );
          setError(
            `Failed to start AI chat: ${chatResponse.status} ${chatResponse.statusText}`
          );
          setStage("ready");
          return;
        }

        console.log("[useAiIntake] Chat started successfully");
      } catch (error) {
        console.error("[useAiIntake] Error in startIntake:", error);
        setError(
          error instanceof Error ? error.message : "Unknown error occurred"
        );
        setStage("ready");
      }
    },
    [openStream, reset, setError, setSessionId, setStage]
  );

  const resumeIntake = useCallback(
    async (projectId: string) => {
      try {
        currentProjectIdRef.current = projectId;
        // Try local cache first
        const cached = safeLocalStorage.getItem(`intake_session_${projectId}`);
        if (cached) {
          setSessionId(cached);
          openStream(cached, projectId);
          setStage("streaming_intake");
          return;
        }

        // Fallback to backend discovery
        const existingResp = await fetch(
          `/api/ai-chat/projects/${projectId}/sessions`,
          { credentials: "include" }
        );

        if (existingResp.ok) {
          const root = await existingResp.json();
          const list: Array<any> = Array.isArray(root)
            ? root
            : Array.isArray(root?.sessions)
            ? root.sessions
            : Array.isArray(root?.data)
            ? root.data
            : [];
          const actives = list.filter((s: any) => s?.status === "active");
          const sorted = actives.sort((a: any, b: any) => {
            const at = new Date(
              a?.updatedAt || a?.updated_at || a?.createdAt || 0
            ).getTime();
            const bt = new Date(
              b?.updatedAt || b?.updated_at || b?.createdAt || 0
            ).getTime();
            return bt - at;
          });
          const active = sorted[0] || list[0];
          const sessId = active?.id || active?.session_id || active?.sessionId;
          if (sessId) {
            setSessionId(sessId);
            try {
              safeLocalStorage.setItem(`intake_session_${projectId}`, sessId);
            } catch {}
            openStream(sessId, projectId);
            setStage("streaming_intake");
          }
        }
      } catch (e) {
        // ignore resume errors
      }
    },
    [openStream, setSessionId, setStage]
  );
  return {
    sessionId,
    stage,
    progress,
    insertedCount,
    error,
    startIntake,
    resumeIntake,
    cleanup,
  };
}
